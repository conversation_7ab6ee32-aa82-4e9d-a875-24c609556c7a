from datetime import datetime, date
from functools import wraps

import boto3
import pytz
from dateutil import parser as date_parser
from flask import flash, redirect, url_for, request, current_app
from flask_admin.contrib.sqla import ModelView
from flask_admin.contrib.sqla.form import AdminModelConverter
from flask_admin.form import DatePickerWidget
from flask_admin.model import typefmt
from flask_admin.model.form import converts
from flask_login import current_user
from flask_mail import Mail
from flask_security import logout_user
from wtforms import DateField

from config import Config

aws_ses_client = boto3.client(
    'ses',
    region_name=Config.AWS_REGION_NAME,
    aws_access_key_id=Config.AWS_ACCESS_KEY,
    aws_secret_access_key=Config.AWS_SECRET_KEY
)
mail = Mail()


def get_current_timezone_time():
    return datetime.now(current_app.config.get('CURRENT_TIMEZONE'))


def date_format(view, value):
    if value is None:
        return value
    try:
        return value.strftime('%d/%m/%Y')
    except ValueError:
        return value


def date_time_format(value):
    if value is None:
        return ''
    return value.strftime('%d/%m/%Y %H:%M %p')


def date_parse(value):
    try:
        return date_parser.parse(value, dayfirst=True)
    except Exception as ex:
        return None


def convert_naive_local_utc(value):
    '''Converts a naive datetime object in local time to UTC date time with no time zone info'''
    # remove timezone info if timezone present
    if value.tzinfo:
        value = value.replace(tzinfo=None)
    local = current_app.config.get('CURRENT_TIMEZONE')
    local_time = local.localize(value)
    utc = pytz.timezone('UTC')
    utc_time = local_time.astimezone(utc).replace(tzinfo=None)
    return utc_time


def convert_naive_utc_local(value):
    '''Converts a naive UTC datetime object to local date time with no time zone info'''
    if value is None:
        return value
    if value and isinstance(value, str):
        value = date_parser.parse(value)
    local = current_app.config.get('CURRENT_TIMEZONE')
    if value.tzinfo:
        value = value.replace(tzinfo=None)
    utc_date = pytz.timezone('UTC').localize(value)
    local_time = utc_date.astimezone(local).replace(tzinfo=None)
    return local_time


def date_time_format_tz_local(value, view=None):
    '''Converts a naive UTC datetime object to local date time and returns the display string'''
    local_time = convert_naive_utc_local(value)
    if local_time is None:
        return None
    if view == 'date':
        return local_time.strftime('%d/%m/%Y')
    return local_time.strftime('%d/%m/%Y %I:%M %p')


def admin_date_time_converter(admin_view, value):
    return date_time_format_tz_local(value, admin_view)


DATE_FORMATTERS = dict(typefmt.BASE_FORMATTERS)
DATE_FORMATTERS.update(
    {
        type(None): typefmt.null_formatter,
        datetime: date_format,
        date: date_format
    }
)

DATE_TIME_FORMATTERS = dict(typefmt.BASE_FORMATTERS)
DATE_TIME_FORMATTERS.update(
    {
        type(None): typefmt.null_formatter,
        datetime: date_time_format
    }
)

DATE_TIME_FORMATTERS_TZ_LOCAL = dict(typefmt.BASE_FORMATTERS)
DATE_TIME_FORMATTERS_TZ_LOCAL.update(
    {
        type(None): typefmt.null_formatter,
        datetime: admin_date_time_converter
    }
)


class AdminSecurityMixin:
    def is_accessible(self):
        if (current_user.is_active  # is current_user account enabled?
                # are they logged in?
                and current_user.is_authenticated
                # do they have the correct roles to login to the admin?
                and current_user.has_role('superuser')):
            return True
        return False

    def _handle_view(self, name, **kwargs):
        # if current_user.is_authenticated :
        #     return redirect(url_for('auth.otp_view'))
        if not self.is_accessible():
            # if user is logged in, but can't view the admin, reject access
            if current_user.is_authenticated:
                flash('You need administrator privileges to access this page')
                logout_user()
                return redirect(url_for('security.login', next=request.url))
            return redirect(url_for('security.login', next=request.url))


class CliniBaseAdminModelConverter(AdminModelConverter):
    @converts('Date')
    def convert_date(self, field_args, **extra):
        field_args['widget'] = DatePickerWidget()
        return DateField(format='%d-%m-%Y', **field_args)


class TrialModelView(AdminSecurityMixin, ModelView):
    model_form_converter = CliniBaseAdminModelConverter
    col_width = {}
    hidden_fields = []
    bootstrap_col_size = {}
    column_type_formatters = DATE_TIME_FORMATTERS_TZ_LOCAL
    hide_name_all = ('roles', 'merge', 'delete patient', 'reports')
    hide_category_all = ('version', 'todo')
    cta_hide = ('internal doctors', 'gps', 'study user roles')
    cta_category_hide = ('scheduler', 'diagnosis')
    emeritus_name_exception = ('holidays',)
    form_excluded_columns = ('created', 'updated')
    column_exclude_list = ('created', 'updated')

    def append_columns(self, columns):
        self.column_list = columns

    def inaccessible_callback(self, name, **kwargs):
        # if current_user.is_authenticated :
        #     return redirect(url_for('auth.otp_view'))
        if not self.is_accessible():
            # if user is logged in, but can't view the admin, reject access
            if current_user.is_authenticated:
                flash('You need administrator privileges to access this page')
                logout_user()
                return redirect(url_for('security.login', next=request.url))
            return redirect(url_for('security.login', next=request.url))

    def col_style(self, col_name):
        return self.col_width.get(col_name) or ''

    def is_visible(self):
        """
            Override this method if you want dynamically hide or show administrative views
            from Flask-Admin menu structure

            By default, item is visible in menu.

            Please note that item should be both visible and accessible to be displayed in menu.
        """
        if current_user.is_anonymous:
            return True
        if current_user.email.endswith('cardiobase.com'):
            return True

        # Hide all
        if self.category.lower() in self.hide_category_all or self.name.lower() in self.hide_name_all:
            return False

        return True


def user_required_role(roles=["ADMIN"]):
    def wrapper(fn):
        @wraps(fn)
        def decorated_view(*args, **kwargs):
            if not current_user.is_authenticated or not current_user.is_active:
                flash(
                    u'Authentication error, you need to be logged in to access this page.', 'auth'
                )
                return redirect(url_for('auth_bp.login_view'))
            return fn(*args, **kwargs)

        return decorated_view

    return wrapper


def safe_int_conversion(value):
    try:
        return int(value)
    except (ValueError, TypeError):
        return None
