import {ComponentProps, useRef, useState} from 'react';
import {
  ComboBox,
  DateInput,
  DateSegment,
  Dialog,
  Group,
  Heading,
  ListBox,
  ListBoxItem,
  Modal,
  Popover,
  Button as RACButton,
  SelectValue,
} from 'react-aria-components';
import {FieldValues, useForm} from 'react-hook-form';
import {useLocation, useNavigate} from 'react-router';
import {AsyncListOptions, useAsyncList} from 'react-stately';

import {useMutation, useQuery} from '@apollo/client';
import {getLocalTimeZone, now, toCalendarDate} from '@internationalized/date';
import {Calendar as CalenderIcon, ChevronDown, FilesIcon, Plus, Undo2Icon, X} from 'lucide-react';

import {QueryResultType} from '@/@types/graphql.tada';
import {apolloClient} from '@/apollo-client.ts';
import UploadIcon from '@/assets/iconly/Upload.svg?react';
import {Input, Label, TextArea} from '@/components/ui/Field.tsx';
import {But<PERSON>} from '@/components/ui/button';
import {Calendar} from '@/components/ui/calendar.tsx';
import {Form, FormField, SelectFormField, TextFormField} from '@/components/ui/form';
import {FieldArray} from '@/components/ui/form/FieldArray/FieldArray';
import {FieldArrayItems} from '@/components/ui/form/FieldArray/FieldArrayItems';
import {FieldError} from '@/components/ui/form/FieldError.tsx';
import {WatchState} from '@/components/ui/form/WatchState.tsx';
import {DateFormField} from '@/components/ui/form/fields/DateFormField.tsx';
import {createStudyRequest, getAllStudyRequest} from '@/graphql/bookings.ts';
import {getDoctorsData} from '@/graphql/contacts.ts';
import {getProceduresList} from '@/graphql/lists.ts';
import {searchPatientsQuery} from '@/graphql/patients.ts';
import authStore from '@/store/auth.store.ts';
import {Patient} from '@/store/patient';
import {axiosInstance} from "@/axios.ts";


export const formatDoctorName = (doctor: DoctorType) => {
  const fullName = [doctor.surname, doctor.forename].filter(Boolean).join(', ').toLowerCase();
  return doctor.title ? `${doctor.title.toLowerCase()}. ${fullName}` : fullName;
};

export function AddReferralDrawer(props: ComponentProps<typeof Modal>) {
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);
  const [selectedRequestingDoctor, setSelectedRequestingDoctor] = useState<string>('');
  const [selectedReportCC, setSelectedReportCC] = useState<string>('');
  const location = useLocation();
  const navigate = useNavigate();

  const form = useForm();

  const {data: procedures} = useQuery(getProceduresList);
  const [createStudyRequestMutation, {loading: isSubmitting}] = useMutation(createStudyRequest);

  const patientsList = useAsyncList<Patient>({
    getKey: (p) => p.patientid,
    load: patientsListLoader,
  });

  const doctorsList = useAsyncList<DoctorType>({
    getKey: (p) => p.id,
    load: doctorListLoader,
  });

  const doctorsCCList = useAsyncList<DoctorType>({
    getKey: (p) => p.id,
    load: doctorListLoader,
  });

  const fileInputRef = useRef(null);
  const [error, setError] = useState<null | string>(null);

  const handleClick = () => {
    (fileInputRef?.current as any)?.click();
  };

  const handleFileInput = (event: any) => {
    const files = event.target.files;
    handleFiles(files);
  };

  const handleDrop = (event: any) => {
    event.preventDefault();
    event.stopPropagation();
    setError(null);

    const files = event.dataTransfer.files;
    handleFiles(files);
  };

  const handleFiles = (files: any) => {
    if (files.length > 0) {
      const file = files[0];
      if (file.type === 'application/pdf' && file.size <= 20 * 1024 * 1024) {
        console.log('Valid PDF file:', file.name);
        form.setValue('pdf_file_url', file);

        const formData = new FormData();
        formData.append('file', file);


        axiosInstance.get('/booking/okay');

        axiosInstance.post('/api/booking/upload', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          }
        })
          .then((response: any) => {
            console.log('File uploaded successfully:', response.data);
          })
          .catch((error: any) => {
            console.error('Error uploading file:', error);
          });
      } else {
        setError(
          file.type !== 'application/pdf' ? 'Only PDF files are allowed.' : 'File size exceeds 20 MB.'
        );
      }
    }
  };

  const handleDragOver = (event: any) => {
    event.preventDefault();
    event.stopPropagation();
  };

  return (
    <Modal
      isDismissable
      isOpen={location.pathname === '/bookings/referrals/add'}
      onOpenChange={() => {
        navigate('/bookings/referrals');
      }}
      className="react-aria-Drawer"
      {...props}
    >
      <Dialog className="react-aria-Dialog flex min-h-full flex-col gap-y-4">
        <div className="no-inset shrink-0 border-b border-neutral-200 text-neutral-800">
          <Heading slot="title">Add Referral</Heading>
          <RACButton slot="close">
            <X />
          </RACButton>
        </div>

        <Form
          className="flex flex-1 flex-col"
          control={form}
          transformData={(data) => {
            const procedures = data.procedures.map((p: any) => ({
              ...p,
              provisional_procedure_date: p.provisional_date?.toDate()?.toISOString(),
            }));
            return {
              ...data,
              referral_date: data.referral_date?.toDate()?.toISOString(),
              procedures,
            };
          }}
          onSubmit={async (data: FieldValues) => {
            try {
              const studyRequestItems = data.procedures.map((procedure: any) => ({
                // procedure_id: parseInt(procedure.procedure_id),
                // procedure_ids: procedure.procedure_ids.map((id: string) => parseInt(id)),
                procedure_ids: [2, 3], // hardcoded for now
                note: procedure.note || null,
                provisional_procedure_date: procedure.provisional_procedure_date || null,
                status: 'pending',
              }));

              await createStudyRequestMutation({
                variables: {
                  study_request: {
                    pdf_file_url: null,
                    type: 'referral',
                    requesting_doctor_id: parseInt(data.requesting_doctor_id),
                    report_cc_id: data.report_cc ? parseInt(data.report_cc) : null,
                    patient_id: parseInt(data.patient_id),
                    site_id: authStore.tokenSiteId,
                    urgency: 'Routine',
                    appointment_note: null,
                    // date_recieved: data.referral_date,
                    study_request_items: {data: studyRequestItems},
                  },
                },
              });
            } catch (error) {
              console.error('Error creating study request:', error);
            }
          }}
          onSubmitSuccess={() => {
            apolloClient.refetchQueries({include: [getAllStudyRequest]});
            navigate('/bookings/referrals');
          }}
        >
          <div className="flex-1">
            <WatchState name="pdf_file_url">
              {(file) => (
                <div
                  className="flex justify-center rounded-lg border-[1.2px] border-dashed border-neutral-300 py-6"
                  onDrop={handleDrop}
                  onDragOver={handleDragOver}
                >
                  <div className="flex flex-col items-center space-y-2.5">
                    <div className="bg-brand-100 flex h-10 w-10 items-center justify-center rounded-full">
                      {file ? (
                        <FilesIcon className="text-brand-700 h-4.5 w-4.5" />
                      ) : (
                        <UploadIcon className="text-brand-700 h-4.5 w-4.5" />
                      )}
                    </div>

                    {file ? (
                      <div>
                        <p className="text-sm text-neutral-800">{file.name}</p>
                      </div>
                    ) : (
                      <div className="flex w-full flex-col items-center justify-center text-neutral-800">
                        <p className="font-semibold">
                          Drag and Drop, or{' '}
                          <a
                            className="text-brand-400 cursor-pointer hover:underline"
                            onClick={handleClick}
                          >
                            click to browse
                          </a>
                        </p>
                        <p className="text-sm text-neutral-700">PDF file, max size 20 MB</p>
                        {error && <p className="text-sm text-red-500">{error}</p>}
                      </div>
                    )}

                    <input
                      type="file"
                      accept="application/pdf"
                      ref={fileInputRef}
                      className="hidden"
                      onChange={handleFileInput}
                    />

                    {file && (
                      <div className="flex items-center gap-x-2">
                        <button
                          className="flex cursor-pointer items-center gap-x-1 text-xs font-semibold text-[#EF4444]"
                          onClick={() => form.setValue('pdf_file_url', null)}
                          type="button"
                        >
                          <X className="h-3 w-3" />
                          Remove
                        </button>
                        <button
                          className="text-brand-500 flex cursor-pointer items-center gap-x-1 rounded-md border border-neutral-300 px-1.5 py-0.5 text-xs font-medium"
                          onClick={handleClick}
                          type="button"
                        >
                          <Undo2Icon className="h-3 w-3" />
                          Reupload
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </WatchState>

            <div className="my-4 text-xs leading-snug font-bold tracking-wide text-neutral-800 uppercase">
              Referral Details
            </div>

            <div className="grid grid-cols-3 gap-4">
              <FormField
                name="patient_id"
                required
              >
                <ComboBox
                  items={patientsList.items}
                  inputValue={patientsList.filterText}
                  onInputChange={patientsList.setFilterText}
                  onSelectionChange={(key) => {
                    if (key) {
                      const patient = patientsList.getItem(key) ?? null;
                      patientsList.setFilterText(patient?.fullName ?? '');
                      setSelectedPatient(patient);
                    } else {
                      setSelectedPatient(null);
                    }
                  }}
                  selectedKey={selectedPatient?.patientid}
                  className="react-aria-ComboBox col-span-2"
                >
                  <Label className="text-neutral-700">Name</Label>
                  <div className="relative">
                    <Input placeholder="Search Patients" />
                    <RACButton className="absolute inset-y-0 right-2 z-20 flex items-center">
                      <ChevronDown className="size-4" />
                    </RACButton>
                  </div>
                  <Popover>
                    <ListBox>
                      {(item: Patient) => <ListBoxItem id={item.patientid}>{item.fullName}</ListBoxItem>}
                    </ListBox>
                  </Popover>
                </ComboBox>
              </FormField>

              <DateFormField
                name="referral_date"
                defaultValue={toCalendarDate(now(getLocalTimeZone()))}
              >
                <Label className="text-neutral-700">Referral Date</Label>
                <Group>
                  <DateInput>{(segment) => <DateSegment segment={segment} />}</DateInput>
                  <RACButton className="react-aria-Button">
                    <CalenderIcon
                      className="h-4 w-4"
                      color="currentColor"
                    />
                  </RACButton>
                </Group>
                <Popover className="react-aria-Popover w-max">
                  <Dialog className="p-4">
                    <Calendar />
                  </Dialog>
                </Popover>
                <FieldError />
              </DateFormField>

              {selectedPatient && (
                <div className="contents divide-x divide-neutral-200">
                  <div>
                    {/* todo: handle MRN Style */}
                    <div className="text-xs leading-relaxed font-semibold text-neutral-800">MRN</div>
                    <div className="mt-1 text-sm text-neutral-800">{selectedPatient?.ur}</div>
                  </div>
                  <div>
                    <div className="text-xs leading-relaxed font-semibold text-neutral-800">Gender</div>
                    <div className="mt-1 text-sm text-neutral-800">
                      {selectedPatient?.genderResolved?.description}
                    </div>
                  </div>
                  <div>
                    <div className="text-xs leading-relaxed font-semibold text-neutral-800">DOB</div>
                    <div className="mt-1 text-sm text-neutral-800">
                      {selectedPatient?.dob} ({selectedPatient?.ageToday?.toFixed(1)} yrs)
                    </div>
                  </div>
                </div>
              )}
            </div>

            <hr className="mt-6 mb-4 text-neutral-100" />

            <div className="my-4 text-xs leading-snug font-bold tracking-wide text-neutral-800 uppercase">
              Doctor Details
            </div>
            <div className="space-y-4">
              <div className="grid grid-cols-3 gap-4">
                <FormField
                  name="requesting_doctor_id"
                >
                  <ComboBox
                    items={doctorsList.items}
                    inputValue={selectedRequestingDoctor || doctorsList.filterText}
                    onInputChange={(value) => {
                      setSelectedRequestingDoctor(''); // Clear selection when typing
                      doctorsList.setFilterText(value);
                    }}
                    onSelectionChange={(key) => {
                      if (key) {
                        const doctor = doctorsList.getItem(key) ?? null;
                        if (doctor) {
                          const displayName = formatDoctorName(doctor);
                          setSelectedRequestingDoctor(displayName);
                          doctorsList.setFilterText(''); // Clear filter to reset list
                        }
                      } else {
                        setSelectedRequestingDoctor('');
                      }
                    }}
                    selectedKey={
                      doctorsList.items.find((d) => formatDoctorName(d) === selectedRequestingDoctor)?.id
                    }
                    className="react-aria-ComboBox col-span-2"
                  >
                    <Label className="text-neutral-700">Requesting Doctor</Label>
                    <div className="relative">
                      <Input placeholder="Search Doctors" />
                      <RACButton className="absolute inset-y-0 right-2 z-20 flex items-center">
                        <ChevronDown className="size-4" />
                      </RACButton>
                    </div>
                    <Popover>
                      <ListBox>
                        {(item: DoctorType) => (
                          <ListBoxItem id={item.id}>{formatDoctorName(item)}</ListBoxItem>
                        )}
                      </ListBox>
                    </Popover>
                  </ComboBox>
                </FormField>
                <WatchState name="requesting_doctor_id">
                  {(doctorId) => {
                    const doctor = doctorsList.items.find((d) => d.id === doctorId);
                    return (
                      <div>
                        <Label className="text-neutral-700">Doctor Provider#</Label>
                        <Input
                          readOnly
                          className="cursor-not-allowed focus:outline-none"
                          value={doctor?.provider_number ?? ''}
                        />
                      </div>
                    );
                  }}
                </WatchState>
              </div>

              <FormField
                name="report_cc"
              >
                <ComboBox
                  items={doctorsCCList.items}
                  inputValue={selectedReportCC || doctorsCCList.filterText}
                  onInputChange={(value) => {
                    setSelectedReportCC(''); // Clear selection when typing
                    doctorsCCList.setFilterText(value);
                  }}
                  onSelectionChange={(key) => {
                    if (key) {
                      const doctor = doctorsCCList.getItem(key) ?? null;
                      if (doctor) {
                        const displayName = formatDoctorName(doctor);
                        setSelectedReportCC(displayName);
                        doctorsCCList.setFilterText(''); // Clear filter to reset list
                      }
                    } else {
                      setSelectedReportCC('');
                    }
                  }}
                  selectedKey={doctorsCCList.items.find((d) => formatDoctorName(d) === selectedReportCC)?.id}
                  className="react-aria-ComboBox col-span-2"
                >
                  <Label className="text-neutral-700">Report CC</Label>
                  <div className="relative">
                    <Input placeholder="Search Doctors" />
                    <RACButton className="absolute inset-y-0 right-2 z-20 flex items-center">
                      <ChevronDown className="size-4" />
                    </RACButton>
                  </div>
                  <Popover>
                    <ListBox>
                      {(item: DoctorType) => <ListBoxItem id={item.id}>{formatDoctorName(item)}</ListBoxItem>}
                    </ListBox>
                  </Popover>
                </ComboBox>
              </FormField>
            </div>

            <hr className="mt-6 mb-4 text-neutral-100" />

            <div className="my-4 text-xs leading-snug font-bold tracking-wide text-neutral-800 uppercase">
              Procedure Details
            </div>

            <FieldArray
              name="procedures"
              initialItemsCount={1}
            >
              {({append}) => (
                <>
                  <FieldArrayItems>
                    {({index}) => (
                      <div className="border-brand-100 my-4 grid grid-cols-2 gap-4 rounded border p-4">
                        <SelectFormField
                          name={`procedures.${index}.procedure_id`}
                          className="react-aria-Select"
                          required
                        >
                          <Label className="text-neutral-700">Procedure</Label>
                          <RACButton className="react-aria-Button w-full rounded-sm">
                            <SelectValue className="react-aria-SelectValue text-sm" />
                            <ChevronDown />
                          </RACButton>
                          <Popover>
                            <ListBox items={procedures?.procedure ?? ([] as any)}>
                              {(item: any) => (
                                <ListBoxItem
                                  id={item?.id}
                                  textValue={`${item.seq_number}. ${item.name}`}
                                >
                                  <div className="overflow-hidden text-ellipsis whitespace-nowrap">
                                    {item.seq_number as number}. {item.name}
                                  </div>
                                </ListBoxItem>
                              )}
                            </ListBox>
                          </Popover>
                        </SelectFormField>

                        <DateFormField
                          name={`procedures.${index}.provisional_date`}
                          granularity="day"
                          required
                        >
                          <Label className="text-neutral-700">Provisional Date</Label>
                          <Group>
                            <DateInput>{(segment) => <DateSegment segment={segment} />}</DateInput>
                            <RACButton className="react-aria-Button">
                              <CalenderIcon
                                className="h-4 w-4"
                                color="currentColor"
                              />
                            </RACButton>
                          </Group>
                          <Popover className="react-aria-Popover w-max">
                            <Dialog className="p-4">
                              <Calendar />
                            </Dialog>
                          </Popover>
                          <FieldError />
                        </DateFormField>

                        <TextFormField
                          name={`procedures.${index}.note`}
                          className="react-aria-TextField col-span-2"
                        >
                          <Label className="text-neutral-700">Test/Clinical Notes</Label>
                          <TextArea size="md" />
                        </TextFormField>
                        <div>
                          <Button
                            className="p-0"
                            variant="plain"
                            color="danger"
                            size="small"
                            slot="remove"
                          >
                            <X data-slot="icon" />
                            Remove
                          </Button>
                        </div>
                      </div>
                    )}
                  </FieldArrayItems>
                  <div className="my-4">
                    <Button
                      variant="plain"
                      color="brand"
                      size="small"
                      onPress={() => append({})}
                    >
                      <Plus data-slot="icon" />
                      Add Procedure
                    </Button>
                  </div>
                </>
              )}
            </FieldArray>
          </div>

          <div className="sticky bottom-0 z-50 w-full border-t border-neutral-00 bg-white p-4">
            <Button
              className="w-full"
              type="submit"
              isDisabled={isSubmitting}
            >
              {isSubmitting ? 'Adding...' : 'Add Referral'}
            </Button>
          </div>
        </Form>
      </Dialog>
    </Modal>
  );
}

export const patientsListLoader: AsyncListOptions<Patient, string>['load'] = async ({filterText}) => {
  const {data} = await apolloClient.query({
    query: searchPatientsQuery,
    variables: {
      searchText: filterText ?? '',
      genderCodes: null,
      siteId: authStore.tokenSiteId?.toString(),
      limit: 20,
      offset: 0,
    },
    fetchPolicy: 'network-only',
  });

  return {
    items: data.search_pas_pt.map((p) => new Patient(p as any, false)),
  };
};

export type DoctorType = QueryResultType<typeof getDoctorsData>['doctors'][number];
export const doctorListLoader: AsyncListOptions<DoctorType, string>['load'] = async ({filterText}) => {
  const {data} = await apolloClient.query({
    query: getDoctorsData,
    variables: {
      nameFilter: filterText ? `%${filterText}%` : '%',
    },
    fetchPolicy: 'network-only',
  });

  return {
    items: data.doctors,
  };
};
