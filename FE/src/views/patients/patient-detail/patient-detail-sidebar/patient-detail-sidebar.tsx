import {ReactNode, useMemo, useState} from 'react';
import {useParams} from 'react-router';

import {useQuery} from '@apollo/client';
import {useLocalStorage} from '@mantine/hooks';
import {format, parseISO} from 'date-fns';
import {Activity, ChevronDown, CirclePlus} from 'lucide-react';

import User from '@/assets/iconly/User.svg?react';
import User2 from '@/assets/iconly/User2.svg?react';
import BankCardIcon from '@/assets/iconly/BankCard.svg?react';
import CalendarIcon from '@/assets/iconly/Calendar.svg?react';
import CalendarTime from '@/assets/iconly/CalendarTime.svg?react';
import CallPhoneIcon from '@/assets/iconly/CallPhone.svg?react';
import EditSquareIcon from '@/assets/iconly/EditSquare.svg?react';
import EmailIcon from '@/assets/iconly/Email.svg?react';
import HomeIcon from '@/assets/iconly/Home.svg?react';
import ManWomanIcon from '@/assets/iconly/ManWoman.svg?react';
import SendIcon from '@/assets/iconly/Send.svg?react';
import TranslateLanguageIcon from '@/assets/iconly/TranslateLanguage.svg?react';
import UsersIcon from '@/assets/iconly/Users.svg?react';
import WavesIcon from '@/assets/iconly/Waves.svg?react';
import WorldLocationIcon from '@/assets/iconly/WorldLocation.svg?react';
import {useDialogState} from '@/components/modal-state-provider.tsx';
import {Card, CardContent} from '@/components/ui/card.tsx';
import {Collapsible, CollapsibleContent, CollapsibleTrigger} from '@/components/ui/collapsible';
import {getAboriginalList, getLanguageList, getNationalities} from '@/graphql/lists.ts';
import {getEthnicities, getPatientsDetailData, getRFTGenders} from '@/graphql/patients';
import {Patient} from '@/store/patient.ts';
import AddEditMrnDrawer from '@/views/patients/components/AddEditMRNDrawer.tsx';

import EditableItem from '../../components/EditableItem.tsx';
import {mutatePatientAddresses, mutatePatientData, mutatePatientName} from './mutation';
import {getPreferenceField} from "@/graphql/preferences.ts";

function CollapsibleElement({
  title,
  children,
  isOpen,
  onOpenChange,
}: {
  title: string;
  children: ReactNode;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}) {
  return (
    <Collapsible
      open={isOpen}
      onOpenChange={onOpenChange}
    >
      <CollapsibleTrigger className="my-5 flex w-full cursor-pointer items-center justify-between text-neutral-700">
        <div className="text-xs font-semibold">{title}</div>
        <ChevronDown className="h-4 w-4 [[data-state=open]_&]:rotate-180" />
      </CollapsibleTrigger>
      <CollapsibleContent className="radix-CollapsibleContent -mx-5 px-3">
        <div className="pb-5">{children}</div>
      </CollapsibleContent>
    </Collapsible>
  );
}

interface CollapsibleStates {
  generalInfo: boolean;
  contactInfo: boolean;
  allMrns: boolean;
  rftInfo: boolean;
}

function PatientDetailSidebar() {
  const {patientId} = useParams();
  const [, setIsOpen] = useDialogState('add-edit-MRN-dialogue');
  const [mrnDetails, setMrnDetails] = useState<{
    defaultValue: string | null;
    healthService: null | number | undefined;
    type: 'add' | 'edit';
  }>({
    defaultValue: null,
    healthService: null,
    type: 'add',
  });

  const [collapsibleStates, setCollapsibleStates] = useLocalStorage<CollapsibleStates>({
    key: 'patient-sidebar-states',
    defaultValue: {
      generalInfo: true,
      contactInfo: true,
      allMrns: true,
      rftInfo: true,
    },
    getInitialValueInEffect: false,
    serialize: (v) => JSON.stringify(v),
    deserialize: (v) =>
      v
        ? (JSON.parse(v) as CollapsibleStates)
        : {
            generalInfo: true,
            contactInfo: true,
            allMrns: true,
            rftInfo: true,
          },
  });

  const handleCollapsibleChange = (section: keyof typeof collapsibleStates, open: boolean) => {
    setCollapsibleStates((prev: typeof collapsibleStates) => ({
      ...prev,
      [section]: open,
    }));
  };

  const {data, loading: isPatientsDataLoading} = useQuery(getPatientsDetailData, {
    variables: {patientId: parseInt(patientId ?? '')},
  });
  const {data: ethnicities, loading: isPatientsDataLoading1} = useQuery(getEthnicities);
  const {data: rftGenders, loading: isPatientsDataLoading2} = useQuery(getRFTGenders);
  const {data: nationalities, loading: isPatientsDataLoading3} = useQuery(getNationalities);
  const {data: aboriginalStatus, loading: isPatientsDataLoading4} = useQuery(getAboriginalList);
  const {data: language, loading: isPatientsDataLoading5} = useQuery(getLanguageList);

  const isLoading =
    (!data && isPatientsDataLoading) ||
    (!ethnicities && isPatientsDataLoading1) ||
    (!rftGenders && isPatientsDataLoading2) ||
    (!nationalities && isPatientsDataLoading3) ||
    (!aboriginalStatus && isPatientsDataLoading4) ||
    (!language && isPatientsDataLoading5);

  const patientDetail = data?.pas_pt?.[0];
  const patientStore = useMemo(() => new Patient(patientDetail ?? ({} as any)), [data]);

  const patientRFTGender = rftGenders?.pred_ref_genders?.find((rftG) =>
    rftG?.id ? rftG.id.toString() === patientDetail?.gender_forrfts_code : false
  )?.id;

  const patientCountryOfBirth = nationalities?.list_nationality?.find(
    (n) => n.code === patientDetail?.countryofbirth_code
  )?.code;

  const patientEthnicityForRFT = ethnicities?.pred_ref_ethnicities?.find(
    (eth) => eth.ethnicityid?.toString() === patientDetail?.race_forrfts_code
  );

  const patientAboriginalStatus = aboriginalStatus?.list_aboriginalstatus?.find(
    (ab) => ab?.code === patientDetail?.aboriginalstatus_code
  )?.code;

  const patientPreferredLang = language?.list_language?.find(
    (lang) => lang?.code === patientDetail?.preferredlanguage_code
  )?.code;

  const {data: titles} = useQuery(getPreferenceField, {variables: {fieldName: 'Titles'}});

  const patientAge = patientStore.ageToday?.toFixed(0);


  const generalInfoConfig = [
    {
      id: patientDetail?.pas_pt_names?.[0]?.title,
      defaultValue: patientDetail?.pas_pt_names?.[0]?.title,
      label: 'Title',
      type: 'select',
      selectableItems:
        titles?.prefs_fields?.[0]?.prefs_fielditems.map((title) => ({
          id: title?.fielditem ?? `title-${Math.random()}`,
          value: title?.fielditem ?? '',
        })) ?? [],
      StartAdornmentIcon: <User2 className="size-5 text-neutral-500" />,
      fieldName: 'title',
      mutateFn: mutatePatientName,
    },
    {
      id: 'surname',
      defaultValue: patientDetail?.pas_pt_names?.[0]?.surname,
      label: 'Surname',
      type: 'text',
      StartAdornmentIcon: <User className="size-5 text-neutral-500" />,
      fieldName: 'surname',
      mutateFn: mutatePatientName,
    },
    {
      id: 'firstname',
      defaultValue: patientDetail?.pas_pt_names?.[0]?.firstname,
      label: 'Firstname',
      type: 'text',
      StartAdornmentIcon: <User className="size-5 text-neutral-500" />,
      fieldName: 'firstname',
      mutateFn: mutatePatientName,
    },
    {
      id: patientDetail?.dob?.toString(),
      defaultValue: patientDetail?.dob
        ? `${format(parseISO('2025-07-22T19:00:00' as string), 'dd MMMM, yyyy')}${patientAge ? ` (${patientAge} yr)` : ''}`
        : '',
      label: 'DOB',
      type: 'date',
      StartAdornmentIcon: <CalendarIcon className="size-5 text-neutral-500" />,
      fieldName: 'dob',
      mutateFn: mutatePatientData,
    },
    {
      id: patientDetail?.gender_code?.toString(),
      defaultValue: patientDetail?.gender_code,
      label: 'Gender',
      type: 'select',
      selectableItems:
        rftGenders?.pred_ref_genders?.map((item) => ({
          id: item?.gender_code ?? `gender-${Math.random()}`,
          value: item?.gender ?? '',
        })) ?? [],
      StartAdornmentIcon: <ManWomanIcon className="size-5 text-neutral-500" />,
      mutateFn: mutatePatientData,
      fieldName: 'gender_code',
    },
    {
      id: patientCountryOfBirth,
      defaultValue: patientCountryOfBirth,
      label: 'Country of Birth',
      type: 'select',
      selectableItems:
        nationalities?.list_nationality?.map((nationality) => ({
          id: nationality?.code ?? `nationality-${Math.random()}`,
          value: nationality?.description ?? '',
        })) ?? [],
      StartAdornmentIcon: <WorldLocationIcon className="size-5 text-neutral-500" />,
      fieldName: 'countryofbirth_code',
      mutateFn: mutatePatientData,
    },
    {
      id: patientPreferredLang,
      defaultValue: patientPreferredLang,
      label: 'Language',
      type: 'select',
      selectableItems: language?.list_language
        ?.filter((l) => l?.code)
        ?.map((lang) => ({
          id: lang.code ?? `lang-${Math.random()}`,
          value: lang?.description ?? '',
        })),
      StartAdornmentIcon: <TranslateLanguageIcon className="size-5 text-neutral-500" />,
      fieldName: 'preferredlanguage_code',
      mutateFn: mutatePatientData,
    },
    {
      id: patientAboriginalStatus,
      defaultValue: patientAboriginalStatus,
      label: 'Aboriginal Status',
      type: 'select',
      selectableItems:
        aboriginalStatus?.list_aboriginalstatus?.map((ab) => ({
          id: ab?.code ?? `ab-${Math.random()}`,
          value: ab?.description ?? '',
        })) ?? [],
      StartAdornmentIcon: <WavesIcon className="size-5 text-neutral-500" />,
      fieldName: 'aboriginalstatus_code',
      mutateFn: mutatePatientData,
    },
    {
      id: patientDetail?.medicare_no?.toString(),
      defaultValue: patientDetail?.medicare_no,
      label: 'Medicare No',
      StartAdornmentIcon: <BankCardIcon className="size-5 text-neutral-500" />,
      fieldName: 'medicare_no',
      mutateFn: mutatePatientData,
      validateFn: (val: string) => (val ? Patient.validateMedicareNumber(val) : undefined),
      errorTitle: 'Invalid Medicare Number',
    },
    {
      id: patientDetail?.medicare_expirydate?.toString(),
      defaultValue: patientDetail?.medicare_expirydate,
      label: 'Medicare Expiry',
      type: 'date',
      granularity: 'month',
      dateFormat: 'MM/yyyy',
      StartAdornmentIcon: <CalendarTime className="size-5 text-neutral-500" />,
      fieldName: 'medicare_expirydate',
      mutateFn: mutatePatientData,
      hideCalendarButton: true,
    },
    {
      id: patientDetail?.death_indicator?.toString(),
      defaultValue: patientDetail?.death_indicator,
      label: 'Death Status',
      StartAdornmentIcon: <Activity className="size-5 text-neutral-500" />,
      fieldName: 'death_indicator',
      mutateFn: mutatePatientData,
    },
    {
      id: patientDetail?.death_date?.toString(),
      defaultValue: patientDetail?.death_date
        ? format(parseISO(patientDetail.death_date as string), 'dd MMMM, yyyy')
        : '',
      label: 'Death Date',
      type: 'date',
      StartAdornmentIcon: <CalendarIcon className="size-5 text-neutral-500" />,
      fieldName: 'death_date',
      mutateFn: mutatePatientData,
    },
  ];
  const contactInfoConfig = [
    {
      id: patientDetail?.phone_work?.toString(),
      defaultValue: patientDetail?.phone_work,
      label: 'work phone',
      StartAdornmentIcon: <CallPhoneIcon className="size-5 text-neutral-500" />,
      fieldName: 'phone_work',
      mutateFn: mutatePatientData,
    },
    {
      id: patientDetail?.phone_mobile?.toString(),
      defaultValue: patientDetail?.phone_mobile,
      label: 'mobile phone',
      StartAdornmentIcon: <CallPhoneIcon className="size-5 text-neutral-500" />,
      fieldName: 'phone_mobile',
      mutateFn: mutatePatientData,
    },
    {
      id: patientDetail?.phone_home?.toString(),
      defaultValue: patientDetail?.phone_home,
      label: 'home phone',
      StartAdornmentIcon: <CallPhoneIcon className="size-5 text-neutral-500" />,
      fieldName: 'phone_home',
      mutateFn: mutatePatientData,
    },
    {
      id: patientDetail?.email?.toString(),
      defaultValue: patientDetail?.email,
      label: 'email',
      StartAdornmentIcon: <EmailIcon className="size-5 text-neutral-500" />,
      fieldName: 'email',
      mutateFn: mutatePatientData,
    },
    {
      id: patientDetail?.pas_pt_addresses?.[0]?.address_1?.toString(),
      defaultValue: patientDetail?.pas_pt_addresses?.[0]?.address_1,
      label: 'address line 1',
      StartAdornmentIcon: <HomeIcon className="size-5 text-neutral-500" />,
      fieldName: 'address_1',
      mutateFn: mutatePatientAddresses,
    },
    {
      id: patientDetail?.pas_pt_addresses?.[0]?.address_2?.toString(),
      defaultValue: patientDetail?.pas_pt_addresses?.[0]?.address_2,
      label: 'address line 2',
      StartAdornmentIcon: <HomeIcon className="size-5 text-neutral-500" />,
      fieldName: 'address_2',
      mutateFn: mutatePatientAddresses,
    },
    {
      id: patientDetail?.pas_pt_addresses?.[0]?.suburb?.toString(),
      defaultValue: patientDetail?.pas_pt_addresses?.[0]?.suburb,
      label: 'Suburb',
      StartAdornmentIcon: <HomeIcon className="size-5 text-neutral-500" />,
      fieldName: 'suburb',
      mutateFn: mutatePatientAddresses,
    },
    {
      id: patientDetail?.pas_pt_addresses?.[0]?.postcode?.toString(),
      defaultValue: patientDetail?.pas_pt_addresses?.[0]?.postcode,
      label: 'Postcode',
      type: 'number',
      StartAdornmentIcon: <SendIcon className="size-5 text-neutral-500" />,
      fieldName: 'postcode',
      mutateFn: mutatePatientAddresses,
    },
  ];

  return (
    <Card className="w-76 shrink-0 space-y-5 rounded-md border-neutral-200 px-5">
      <CardContent className="divide-y divide-neutral-200 p-0">
        {/* General Info */}
        <CollapsibleElement
          title="General Info"
          isOpen={collapsibleStates?.generalInfo}
          onOpenChange={(open) => handleCollapsibleChange('generalInfo', open)}
        >
          {generalInfoConfig.map((item, index) => (
            <EditableItem
              key={`general-info-${item.fieldName}-${index}-${Math.random()}`}
              defaultValue={item.defaultValue}
              label={item.label}
              type={item?.type as 'number' | 'date' | 'select' | 'text' | undefined}
              selectableItems={item?.selectableItems}
              StartAdornmentIcon={item.StartAdornmentIcon}
              fieldName={item.fieldName}
              mutateFn={item.mutateFn}
              isPatientDataLoading={isLoading}
              granularity={item?.granularity}
              dateFormat={item?.dateFormat}
              validateFn={item?.validateFn}
              hideCalendarButton={item?.hideCalendarButton}
              errorTitle={item?.errorTitle}
            />
          ))}
        </CollapsibleElement>

        {/* Contact Info */}
        <CollapsibleElement
          title="Contact Info"
          isOpen={collapsibleStates.contactInfo}
          onOpenChange={(open) => handleCollapsibleChange('contactInfo', open)}
        >
          {contactInfoConfig.map((item, index) => (
            <EditableItem
              key={`contact-info-${item.fieldName}-${index}-${Math.random()}`}
              defaultValue={item.defaultValue}
              label={item.label}
              type={item?.type as 'number' | 'date' | 'text' | undefined}
              StartAdornmentIcon={item.StartAdornmentIcon}
              fieldName={item.fieldName}
              mutateFn={item.mutateFn}
              isPatientDataLoading={isLoading}
              dateFormat={(item as any)?.dateFormat}
              granularity={(item as any)?.granularity}
            />
          ))}
        </CollapsibleElement>

        {/* ALL MRN */}
        <Collapsible
          className="relative"
          open={collapsibleStates.allMrns}
          onOpenChange={(open) => handleCollapsibleChange('allMrns', open)}
        >
          <button
            onClick={() => {
              setMrnDetails({
                defaultValue: null,
                healthService: null,
                type: 'add',
              });
              setIsOpen(true);
            }}
            className="absolute left-14 cursor-pointer"
          >
            <CirclePlus className="text-brand-500 h-3.5 w-3.5" />
          </button>
          <CollapsibleTrigger className="my-5 flex w-full cursor-pointer items-center justify-between text-neutral-700">
            <p className="text-xs font-semibold">All MRNs</p>
            <ChevronDown className="h-4 w-4 [[data-state=open]_&]:rotate-180" />
          </CollapsibleTrigger>
          <CollapsibleContent className="radix-CollapsibleContent -mx-5 space-y-1 px-3 pb-5">
            {patientDetail?.pas_pt_ur_numbers?.map((ur) => (
              <div
                className="group flex w-full items-start justify-between rounded-md px-2 py-2 text-xs font-normal text-neutral-900 capitalize hover:bg-neutral-100"
                key={ur.ur_id}
              >
                <div className="space-y-1">
                  <div>
                    {ur.ur} - {ur?.health_service?.[0]?.description}
                  </div>
                  <p className="text-neutral-700">{ur.ur_status}</p>
                </div>
                {!isLoading && (
                  <button
                    className="hidden cursor-pointer group-hover:flex hover:flex"
                    onClick={() => {
                      setMrnDetails({
                        defaultValue: ur.ur,
                        healthService: ur?.health_service?.[0]?.code,
                        type: 'edit',
                      });
                      setIsOpen(true);
                    }}
                  >
                    <EditSquareIcon className="text-brand-500 size-5" />
                  </button>
                )}
              </div>
            ))}
          </CollapsibleContent>
        </Collapsible>

        {/* RFT Info */}
        <CollapsibleElement
          title="RFT Info"
          isOpen={collapsibleStates.rftInfo}
          onOpenChange={(open) => handleCollapsibleChange('rftInfo', open)}
        >
          <EditableItem
            key={`rft-1`}
            defaultValue={patientEthnicityForRFT?.ethnicityid?.toString()}
            label="Ethnicity for RFT"
            type="select"
            selectableItems={
              ethnicities?.pred_ref_ethnicities?.map((eth) => ({
                id: eth?.ethnicityid?.toString() ?? `eth-${Math.random()}`,
                value: eth?.description ?? '',
              })) ?? []
            }
            StartAdornmentIcon={<UsersIcon className="size-5 text-neutral-500" />}
            fieldName="race_forrfts_code"
            mutateFn={mutatePatientData}
            isPatientDataLoading={isLoading}
            dateFormat={null}
          />

          <EditableItem
            key={`rft-2`}
            defaultValue={patientRFTGender?.toString()}
            label="Sex for RFT"
            type="select"
            selectableItems={
              rftGenders?.pred_ref_genders?.map((gender) => ({
                id: gender?.id?.toString() ?? `rftGender-${Math.random()}`,
                value: gender.description ?? '',
              })) ?? []
            }
            StartAdornmentIcon={<ManWomanIcon className="size-5 text-neutral-500" />}
            fieldName="gender_forrfts_code"
            mutateFn={mutatePatientData}
            isPatientDataLoading={isLoading}
            dateFormat={null}
          />
        </CollapsibleElement>
      </CardContent>
      <AddEditMrnDrawer
        type={mrnDetails?.type}
        healthService={mrnDetails?.healthService}
        defaultValue={mrnDetails?.defaultValue}
      />
    </Card>
  );
}

export default PatientDetailSidebar;
