import {
  DateInput,
  DatePicker,
  DateSegment,
  Dialog,
  Group,
  Heading,
  ListBox,
  ListBoxItem,
  Modal,
  Popover,
  Button as RACButton,
  SelectValue,
  TimeField,
} from 'react-aria-components';
import {useNavigate} from 'react-router';

import {useApolloClient, useMutation, useQuery} from '@apollo/client';
import {getLocalTimeZone, now, parseDate, toCalendarDateTime} from '@internationalized/date';
import {format} from 'date-fns';
import {ChevronDown, X} from 'lucide-react';

import {QueryResultType} from '@/@types/graphql.tada.ts';
import {Paths} from '@/api-types/routePaths';
import {CalendarIconly} from '@/components/icons/CalenderIconly.tsx';
import {LockIconly} from '@/components/icons/LockIconly';
import {useDialogState} from '@/components/modal-state-provider.tsx';
import {Input, Label, TextArea} from '@/components/ui/Field';
import {But<PERSON>} from '@/components/ui/button';
import {Calendar} from '@/components/ui/calendar';
import {
  Form,
  FormField,
  FormRootErrors,
  NumberFormField,
  SelectFormField,
  TextFormField,
} from '@/components/ui/form';
import {FieldError} from '@/components/ui/form/FieldError';
import {WatchState} from '@/components/ui/form/WatchState.tsx';
import {
  getPatientsDetailData,
  getPatientsRSessionData,
  getPatientsRespiratoryLabData,
  insertRFTRoutine,
  insert_session,
  update_session,
  updatePatientGender,
  updatePatientEthnicity,
} from '@/graphql/patients';
import {getPreferenceField} from '@/graphql/preferences.ts';
import {useApiQuery} from '@/hooks/use-api-query';
import {globalStore} from '@/store/global.store.ts';

export default function AddEditTestSession({
  admissionsStatus,
  patientDetail,
  smokingHistory,
  reportCopyTo,
}: {
  referralMOAddresses: QueryResultType<typeof getPreferenceField>['prefs_fields'][0]['prefs_fielditems'];
  smokingHistory: QueryResultType<typeof getPreferenceField>['prefs_fields'][0]['prefs_fielditems'];
  admissionsStatus: QueryResultType<typeof getPreferenceField>['prefs_fields'][0]['prefs_fielditems'];
  patientDetail: QueryResultType<typeof getPatientsDetailData>;
  reportCopyTo: QueryResultType<typeof getPreferenceField>['prefs_fields'][0]['prefs_fielditems'];
  type: string;
}) {
  const [isOpen, setIsOpen, testSession] = useDialogState('add-test-session');
  const [insertSession] = useMutation(insert_session);
  const [updateSession] = useMutation(update_session, {
    refetchQueries: [getPatientsDetailData],
  });
  const [updateGender_] = useMutation(updatePatientGender, {
    refetchQueries: [getPatientsDetailData],
  });
  const [updateEthnicity_] = useMutation(updatePatientEthnicity, {
    refetchQueries: [getPatientsDetailData],
  });
  const [insertRft] = useMutation(insertRFTRoutine);
  const client = useApolloClient();

  const {data: referralMO} = useQuery(getPreferenceField, {variables: {fieldName: 'ReferringMO name'}});
  const {data: currentUser} = useApiQuery(Paths.PROFILE);

  const navigate = useNavigate();

  const smokingHx = testSession?.smoke_hx
    ? smokingHistory.find((s) => s.fielditem === testSession.smoke_hx)?.prefs_id
    : null;

  const hasPatientData = (field: string): boolean => {
    if (!patientDetail?.pas_pt?.[0]) return false;

    return (
      field in patientDetail.pas_pt[0] &&
      !!patientDetail.pas_pt[0][field as keyof (typeof patientDetail.pas_pt)[0]]
    );
  };

  const getDateValue = (dateString: string) => {
    return dateString ? parseDate(dateString) : null;
  };

  const calculatePackYears = (cigsPerDay: number, yearsSmoked: number) => {
    if (!cigsPerDay || !yearsSmoked) return null;
    return (cigsPerDay * yearsSmoked) / 20;
  };

  return (
    <Modal
      isOpen={isOpen}
      onOpenChange={setIsOpen}
      className="react-aria-Drawer"
    >
      <Dialog>
        <Heading slot="title">{testSession ? 'Edit test' : 'Add a new test'}</Heading>
        <RACButton slot="close">
          <X />
        </RACButton>
        <Form
          className="mt-6"
          defaultValues={{
            ...testSession,
            admission_status: testSession
              ? admissionsStatus.find((s) => s.fielditem === testSession.admissionstatus)?.prefs_id
              : null,
            birth_date: testSession?.birthdate ? parseDate(testSession.birthdate) : null,
            session_date: testSession?.testdate
              ? getDateValue(testSession.testdate)
              : toCalendarDateTime(now(getLocalTimeZone())),
            request_date: testSession?.req_date ? getDateValue(testSession.req_date) : null,
            smoker_history: smokingHx,
            gender: Number(patientDetail.pas_pt[0]?.gender_forrfts_code),
            ethnicity: Number(patientDetail.pas_pt[0]?.race_forrfts_code),
            height: testSession?.height ? Number(testSession.height) : null,
            last_smoked: testSession?.smoke_last || '',
            cigs_per_day: testSession?.smoke_cigsperday ? Number(testSession.smoke_cigsperday) : null,
            years_smoked: testSession?.smoke_yearssmoked ? Number(testSession.smoke_yearssmoked) : null,
            notes: testSession?.req_clinicalnotes || '',
            req_name: testSession?.req_name || '',
            provider_number: testSession?.req_providernumber || '',
            address: testSession?.req_address || '',
            phone_number: testSession?.req_phone || '',
            fax: testSession?.req_fax || '',
            copy_of_report_to: testSession?.report_copyto
              ? reportCopyTo.find((r) => r.fielditem === testSession.report_copyto)?.prefs_id
              : null,
          }}
          onSubmit={async (data) => {
            try {
              const variables = {
                patientid: patientDetail.pas_pt[0].patientid,
                testdate: data.session_date ? format(new Date(data.session_date), 'yyyy-MM-dd') : null,
                lab: null,
                height: data.height?.toString() || null,
                weight: data.weight?.toString() || null,
                req_name: data.req_name,
                req_address: data.address || null,
                req_providernumber: data.provider_number || null,
                req_healthservice_text: null,
                req_healthservice_code: null,
                req_date: data.request_date ? format(new Date(data.request_date), 'yyyy-MM-dd') : null,
                req_time: null,
                req_phone: data.phone_number || null,
                req_fax: data.fax || '',
                req_email: '',
                req_clinicalnotes: data.notes || null,
                smoke_hx: smokingHistory.find((s) => s.prefs_id === data.smoker_history)?.fielditem || null,
                smoke_cigsperday: data.cigs_per_day?.toString() || null,
                smoke_yearssmoked: data.years_smoked?.toString() || null,
                smoke_packyears:  calculatePackYears(Number(data.cigs_per_day), Number(data.years_smoked))?.toString() || null,
                smoke_last: data.last_smoked || null,
                diagnosticcategory: '',
                pred_sourceids: '',
                admissionstatus:
                  admissionsStatus.find((s) => s.prefs_id === data.admission_status)?.fielditem || null,
                report_copyto: data.copy_of_report_to || null,
                report_copyto_2: null,
                billing_billedto: null,
                billing_billingmo: null,
                billing_billingmoproviderno: null,
                lastupdated_session: new Date().toISOString(),
                lastupdatedby_session: currentUser?.email,
              };

              let sessionId;

              if (testSession) {
                await updateSession({
                  variables: {
                    sessionId: testSession.sessionid,
                    ...variables,
                  },
                });

                sessionId = testSession.sessionid;
              } else {
                const insertResult = await insertSession({variables});
                sessionId = insertResult?.data?.insert_r_sessions_one?.sessionid;
              }

              let rft, rftId;
              if (sessionId && !testSession) {
                rft = await insertRft({
                  variables: {
                    sessionId: sessionId,
                    patientId: patientDetail.pas_pt[0].patientid,
                    testtime: data.session_time ? format(new Date(data.session_time), 'HH:mm') : null,
                  },
                });

                rftId = rft?.data?.insert_rft_routine_one?.rftid;
              }

              const existingGender = patientDetail.pas_pt[0]?.gender_forrfts_code;
              if (data.gender != null && Number(data.gender) !== Number(existingGender)) {
                await updateGender_({
                  variables: {
                    patientId: patientDetail.pas_pt[0].patientid,
                    genderCode: data.gender?.toString(),
                  },
                });
              }

              const existingEthnicity = patientDetail.pas_pt[0]?.race_forrfts_code;
              if (data.ethnicity != null && Number(data.ethnicity) !== Number(existingEthnicity)) {
                await updateEthnicity_({
                  variables: {
                    patientId: patientDetail.pas_pt[0].patientid,
                    raceCode: data.ethnicity?.toString(),
                  },
                });
              }

              await client.refetchQueries({
                include: [getPatientsRespiratoryLabData, getPatientsRSessionData],
              });
              setIsOpen(false);
              rftId && navigate(`/patients/${patientDetail.pas_pt[0].patientid}/rft/${rftId}?edit=true`);
            } catch (error) {
              console.error('Error saving session:', error);
            }
          }}
        >
          <FormRootErrors />
          <div className="divide-y divide-neutral-200">
            <div className="space-y-4 pt-3 pb-4">
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  name="session_date"
                  required
                >
                  <DatePicker
                    granularity="day"
                    hourCycle={24}
                    shouldForceLeadingZeros
                  >
                    <Label>Test date</Label>
                    <Group className="react-aria-Group h-8 w-full rounded-sm">
                      <DateInput className="react-aria-DateInput text-[13px]">
                        {(segment) => <DateSegment segment={segment} />}
                      </DateInput>
                      <RACButton className="react-aria-Button group">
                        <CalendarIconly
                          className="group-data-[focus-visible]:text-brand2-500 h-4.5 w-4.5 text-neutral-600"
                          strokeWidth={1.5}
                        />
                      </RACButton>
                    </Group>
                    <Popover className="react-aria-Popover w-max">
                      <Dialog className="p-4">
                        <Calendar />
                      </Dialog>
                    </Popover>
                    <FieldError />
                  </DatePicker>
                </FormField>

                {!testSession ? (
                  <FormField
                    name="session_time"
                    defaultValue={toCalendarDateTime(now(getLocalTimeZone()))}
                    required
                  >
                    <TimeField hourCycle={24}>
                      <Label>Test Time</Label>
                      <Group className="react-aria-Group h-8 w-full rounded-sm">
                        <DateInput className="react-aria-DateInput text-[13px]">
                          {(segment) => <DateSegment segment={segment} />}
                        </DateInput>
                      </Group>

                      <FieldError />
                    </TimeField>
                  </FormField>
                ) : (
                  <div />
                )}

                <SelectFormField
                  name="admission_status"
                  placeholder="Select Admission Status"
                >
                  <Label>Admission status</Label>
                  <RACButton className="react-aria-Button h-8 w-full rounded-sm">
                    <SelectValue className="react-aria-SelectValue text-sm" />
                    <ChevronDown />
                  </RACButton>
                  <Popover>
                    <ListBox>
                      {admissionsStatus.map((item) => (
                        <ListBoxItem
                          key={item.fielditem}
                          id={item.prefs_id}
                          textValue={item.fielditem?.toString()}
                        >
                          <div className="overflow-hidden text-ellipsis whitespace-nowrap">
                            {item.fielditem}
                          </div>
                        </ListBoxItem>
                      ))}
                    </ListBox>
                  </Popover>
                </SelectFormField>

                <FormField
                  name="birth_date"
                  required={!hasPatientData('dob')}
                >
                  <DatePicker
                    granularity="day"
                    hourCycle={24}
                    shouldForceLeadingZeros
                    isDisabled={hasPatientData('dob')}
                    value={hasPatientData('dob') ? parseDate(String(patientDetail?.pas_pt[0].dob)) : undefined}
                  >
                    <Label>Date of Birth</Label>
                    <Group className="react-aria-Group h-8 w-full rounded-sm">
                      <DateInput>{(segment) => <DateSegment segment={segment} />}</DateInput>
                      <RACButton className="react-aria-Button group">
                        {hasPatientData('dob') ? (
                          <LockIconly className="group-data-[focus-visible]:text-brand2-500 h-4.5 w-4.5 text-neutral-600" />
                        ) : (
                          <CalendarIconly
                            className="group-data-[focus-visible]:text-brand2-500 h-4.5 w-4.5 text-neutral-600"
                            strokeWidth={1.5}
                          />
                        )}
                      </RACButton>
                    </Group>
                    <Popover className="react-aria-Popover w-max">
                      <Dialog className="p-4">
                        <Calendar />
                      </Dialog>
                    </Popover>
                    <FieldError />
                  </DatePicker>
                </FormField>

                <SelectFormField
                  name="gender"
                  required={!hasPatientData('gender_forrfts_code')}
                  placeholder="Sex for RFT"
                  isDisabled={hasPatientData('gender_forrfts_code')}
                >
                  <Label>Sex for RFT</Label>
                  <RACButton className="react-aria-Button h-8 w-full rounded-sm">
                    <SelectValue className="react-aria-SelectValue text-sm" />
                    {hasPatientData('gender_forrfts_code') ? (
                      <LockIconly className="group-data-[focus-visible]:text-brand2-500 h-4.5 w-4.5 text-neutral-600" />
                    ) : (
                      <ChevronDown />
                    )}
                  </RACButton>
                  <FieldError />

                  <Popover>
                    <ListBox items={globalStore.genders.filter((g) => g.list_option_for_rfts)}>
                      {(g) => (
                        <ListBoxItem
                          key={g.id}
                          id={g.id}
                        >
                          {g.description}
                        </ListBoxItem>
                      )}
                    </ListBox>
                  </Popover>
                </SelectFormField>

                <SelectFormField
                  placeholder="Ethnicity for RFT"
                  isDisabled={hasPatientData('race_forrfts_code')}
                  name="ethnicity"
                  required={!hasPatientData('race_forrfts_code')}
                >
                  <Label>Ethnicity for RFT</Label>
                  <RACButton className="react-aria-Button h-8 w-full rounded-sm">
                    <SelectValue className="react-aria-SelectValue text-sm" />
                    {hasPatientData('race_forrfts_code') ? (
                      <LockIconly className="group-data-[focus-visible]:text-brand2-500 h-4.5 w-4.5 text-neutral-600" />
                    ) : (
                      <ChevronDown />
                    )}
                  </RACButton>
                  <FieldError />
                  <Popover>
                    <ListBox items={globalStore.ethnicities.filter((g) => g.list_option_for_demographics)}>
                      {(g) => (
                        <ListBoxItem
                          key={g.id}
                          id={g.id}
                        >
                          {g.description}
                        </ListBoxItem>
                      )}
                    </ListBox>
                  </Popover>
                </SelectFormField>

                <NumberFormField
                  defaultValue={testSession?.height ? Number(testSession.height) : null}
                  name="height"
                  formatOptions={{
                    style: 'unit',
                    unit: 'centimeter',
                    unitDisplay: 'short',
                  }}
                >
                  <Label>Height (cm)</Label>
                  <Input
                    placeholder="Enter height (cm)"
                    className="react-aria-Input h-8 rounded-sm text-neutral-800"
                  />
                </NumberFormField>
                <NumberFormField
                  defaultValue={testSession?.weight ? Number(testSession.weight) : null}
                  name="weight"
                  formatOptions={{
                    style: 'unit',
                    unit: 'kilogram',
                    unitDisplay: 'short',
                  }}
                >
                  <Label>Weight (kg)</Label>
                  <Input
                    placeholder="Enter Weight (Kg)"
                    className="react-aria-Input h-8 rounded-sm text-neutral-800"
                  />
                </NumberFormField>

                <SelectFormField
                  name="smoker_history"
                  className="react-aria-Select"
                  placeholder="Select Smoker History"
                >
                  <Label>Smoker History</Label>
                  <RACButton className="react-aria-Button h-8 w-full rounded-sm">
                    <SelectValue className="react-aria-SelectValue text-sm" />
                    <ChevronDown />
                  </RACButton>
                  <Popover>
                    <ListBox>
                      {smokingHistory.map((r) => (
                        <ListBoxItem
                          key={r.prefs_id ?? ''}
                          id={r.prefs_id}
                        >
                          {r.fielditem}
                        </ListBoxItem>
                      ))}
                    </ListBox>
                  </Popover>
                </SelectFormField>

                <div />

                <WatchState name="smoker_history">
                  {(smokingHistoryId) => {
                    if (smokingHistoryId === 88 || !smokingHistoryId) {
                      return null;
                    }
                    return (
                      <>
                        <TextFormField name="last_smoked">
                          <Label>Last Smoked</Label>
                          <Input
                            placeholder="Enter last smoked date"
                            className="react-aria-Input h-8 rounded-sm text-neutral-800"
                          />
                        </TextFormField>

                        <NumberFormField name="cigs_per_day">
                          <Label>Cigarettes per day</Label>
                          <Input
                            placeholder="Enter cigarettes per day"
                            className="react-aria-Input h-8 rounded-sm text-neutral-800"
                          />
                        </NumberFormField>

                        <NumberFormField name="years_smoked">
                          <Label>Years smoked</Label>
                          <Input
                            placeholder="Enter years smoked"
                            className="react-aria-Input h-8 rounded-sm text-neutral-800"
                          />
                        </NumberFormField>

                        <WatchState name="cigs_per_day">
                          {(cigsPerDay) => (
                            <WatchState name="years_smoked">
                              {(yearsSmoked) => {
                                const packYears = calculatePackYears(Number(cigsPerDay), Number(yearsSmoked));
                                return (
                                  <NumberFormField
                                    name="pack_years"
                                    defaultValue={packYears}
                                    isDisabled={true}
                                  >
                                    <Label>Pack years (calculated)</Label>
                                    <Input
                                      value={packYears?.toFixed(1) || ''}
                                      className="react-aria-Input h-8 rounded-sm disabled:text-neutral-700"
                                      readOnly
                                    />
                                  </NumberFormField>
                                );
                              }}
                            </WatchState>
                          )}
                        </WatchState>
                      </>
                    );
                  }}
                </WatchState>

                <TextFormField
                  name="notes"
                  className="react-aria-TextField col-span-2"
                >
                  <Label>Clinical notes / Reason for tests</Label>
                  <TextArea
                    className="react-aria-TextArea rounded-sm"
                    placeholder="Enter notes here..."
                  />
                  <FieldError />
                </TextFormField>
              </div>
            </div>

            <div className="space-y-4 pt-3 pb-4">
              <div className="text-sm font-semibold text-neutral-800 uppercase">Requested by</div>
              <div className="grid grid-cols-2 gap-4">
                <FormField name="request_date">
                  <DatePicker
                    granularity="day"
                    hourCycle={24}
                    shouldForceLeadingZeros
                    className="react-aria-DatePicker"
                    isDisabled={hasPatientData('req_date')}
                  >
                    <Label>Request date</Label>
                    <Group className="react-aria-Group h-8 w-full rounded-sm">
                      <DateInput>{(segment) => <DateSegment segment={segment} />}</DateInput>
                      <RACButton className="react-aria-Button group">
                        <CalendarIconly
                          className="group-data-[focus-visible]:text-brand2-500 h-4.5 w-4.5 text-neutral-600"
                          strokeWidth={1.5}
                        />
                      </RACButton>
                    </Group>
                    <Popover className="react-aria-Popover w-max">
                      <Dialog className="p-4">
                        <Calendar />
                      </Dialog>
                    </Popover>
                    <FieldError />
                  </DatePicker>
                </FormField>

                <div />

                <SelectFormField
                  name="req_name"
                  className="react-aria-Select"
                  placeholder="Select Medical Officer"
                >
                  <Label>Requesting MO</Label>
                  <RACButton className="react-aria-Button h-8 w-full rounded-sm">
                    <SelectValue className="react-aria-SelectValue text-sm" />
                    <ChevronDown />
                  </RACButton>
                  <Popover>
                    <ListBox items={referralMO?.prefs_fields[0]?.prefs_fielditems ?? []}>
                      {(item) => (
                        <ListBoxItem
                          key={item.fielditem}
                          id={item.fielditem ?? ''}
                        >
                          <div className="max-w-full overflow-hidden text-ellipsis whitespace-nowrap">
                            {item.fielditem}
                          </div>
                        </ListBoxItem>
                      )}
                    </ListBox>
                  </Popover>
                </SelectFormField>

                <TextFormField name="provider_number">
                  <Label>Provider Number</Label>
                  <Input
                    placeholder="Enter Provider Number"
                    className="react-aria-Input h-8 rounded-sm text-neutral-800"
                  />
                </TextFormField>

                <TextFormField
                  name="address"
                  className="react-aria-TextField col-span-2"
                >
                  <Label>Address</Label>
                  <TextArea
                    placeholder="Enter address"
                    className="react-aria-Input field-sizing-content w-full rounded-sm text-neutral-800"
                  />
                </TextFormField>

                <TextFormField name="phone_number">
                  <Label>Phone Number</Label>
                  <Input
                    placeholder="Enter Phone Number"
                    className="react-aria-Input h-8 rounded-sm text-neutral-800"
                  />
                </TextFormField>

                <TextFormField name="fax">
                  <Label>Fax</Label>
                  <Input className="react-aria-Input h-8 rounded-sm text-neutral-800" />
                </TextFormField>

                <SelectFormField
                  name="copy_of_report_to"
                  className="react-aria-Select"
                  placeholder="Select"
                >
                  <Label>Copy of report to</Label>
                  <RACButton className="react-aria-Button h-8 w-full rounded-sm">
                    <SelectValue className="react-aria-SelectValue text-sm" />
                    <ChevronDown />
                  </RACButton>
                  <FieldError />
                  <Popover>
                    <ListBox>
                      {reportCopyTo.map((r) => (
                        <ListBoxItem
                          key={r.prefs_id}
                          id={r.prefs_id}
                        >
                          {r.fielditem}
                        </ListBoxItem>
                      ))}
                    </ListBox>
                  </Popover>
                </SelectFormField>
              </div>
            </div>
          </div>

          <div className="sticky bottom-0 z-20 flex w-full justify-end gap-4 border-t border-neutral-100 bg-white p-4 sm:px-6">
            <Button
              type="reset"
              variant="outlined"
              onPress={() => setIsOpen(false)}
            >
              Cancel
            </Button>
            <Button type="submit">{testSession ? 'Update' : 'Continue'}</Button>
          </div>
        </Form>
      </Dialog>
    </Modal>
  );
}
