import {Dialog, Modal} from 'react-aria-components';
import {Link, useNavigate} from 'react-router';

import {Paths} from '@/api-types/routePaths';
import {Input, Label} from '@/components/ui/Field.tsx';
import {Button} from '@/components/ui/button';
import {FieldError, Form, FormRootErrors, TextFormField} from '@/components/ui/form';
import {apiQueryOptions} from '@/hooks/use-api-query.ts';
import {DummyAppLayout} from '@/layouts/DummyAppLayout.tsx';
import {queryClient} from '@/query-client.ts';
import authStore from '@/store/auth.store.ts';
import {PatientsViewInner} from '@/views/patients/patients-search.tsx';
import {getCountryFromTimezone} from '@/lib/utils.ts';

export default function RegisterPage() {
  const navigate = useNavigate();
  const country = getCountryFromTimezone()
  const ROOT_ERROR_FIELD = '__root__';

  return (
    <DummyAppLayout>
      <PatientsViewInner />
      <Modal
        isOpen
        className="react-aria-Modal w-130 overflow-y-auto"
      >
        <Dialog>
          <div className="mb-4">
            <img
              src="/dark-logo.png"
              alt="Rezibase"
              className="mx-auto h-6"
            />
          </div>
          <p className="mb-6 text-center text-sm text-gray-700">
            You are almost in! Just fill in a few key details so we can create your account.
          </p>

          <Form
            action={Paths.REGISTER}
            method="POST"
            transformData={(data) => {
              const name = data['name'];
              delete data['name'];
              data['first_name'] = name.split(' ')[0];
              data['last_name'] = name.split(' ').slice(1).join(' ');

              data['onboarding_demographics'] = {country: country};
              return data;
            }}
            onSubmitSuccess={async (res) => {
              await authStore.login(res.data.access_token, res.data.refresh_token);
              const result = await queryClient.fetchQuery(apiQueryOptions(Paths.SITES_LIST));
              localStorage.setItem('rezibase:site_id', String(result[0]?.value));
              return navigate('/');
            }}
            onSubmitError={(error: any, form) => {
              const errors = error?.response?.data?.errors;

              if (errors) {
                Object.entries(errors).forEach(([field, message]) => {
                  form.setError(field, { message: message as string });
                });
              } else if (error?.response?.data?.message) {
                form.setError(ROOT_ERROR_FIELD, { message: error.response.data.message as string });
              } else {
                form.setError(ROOT_ERROR_FIELD, { message: 'Something went wrong. Please try again.' });
              }
            }}
          >
            <div className="space-y-3.5">
              <FormRootErrors />
              <TextFormField
                name="site_name"
                required
              >
                <Label>Site Name</Label>
                <Input
                  size="lg"
                  placeholder="Enter your site name"
                />
                <FieldError />
              </TextFormField>
              <TextFormField
                name="name"
                required
              >
                <Label>Your Name</Label>
                <Input
                  id="name"
                  autoComplete="name"
                  size="lg"
                  placeholder="Enter your full name"
                />
                <FieldError />
              </TextFormField>
              <TextFormField
                name="email"
                pattern="email"
                required
              >
                <Label>Email</Label>
                <Input
                  id="email"
                  type="email"
                  size="lg"
                  placeholder="Enter your email address"
                />
                <FieldError />
              </TextFormField>
              <TextFormField
                name="us_phone_number"
                required
              >
                <Label>Phone</Label>
                <Input
                  size="lg"
                  placeholder="Enter your phone"
                />
                <FieldError />
              </TextFormField>
              <TextFormField
                name="password"
                required
              >
                <Label>Password</Label>
                <Input
                  id="password"
                  type="password"
                  size="lg"
                  placeholder="Enter your password"
                />
                <FieldError />
              </TextFormField>
              <TextFormField
                name="password_confirm"
                required
              >
                <Label htmlFor="password_confirm">Confirm Password</Label>
                <Input
                  id="password_confirm"
                  type="password"
                  size="lg"
                  placeholder="Confirm your password"
                />
                <FieldError />
              </TextFormField>
              <div className="pt-2">
                <Button
                  type="submit"
                  className="h-10 w-full rounded-sm"
                >
                  Start free trial
                </Button>
              </div>
            </div>
          </Form>
          <div className="mt-6 text-center text-sm text-neutral-900">
            Already have an account?{' '}
            <Link
              to="/auth/login"
              className="text-brand-500 font-semibold hover:underline"
            >
              Sign in Here
            </Link>
          </div>
        </Dialog>
      </Modal>
    </DummyAppLayout>
  );
}
