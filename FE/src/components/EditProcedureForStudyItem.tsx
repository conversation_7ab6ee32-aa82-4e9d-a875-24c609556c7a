import {
  DateInput,
  DateSegment,
  Dialog,
  Group,
  ListBox,
  ListBoxItem,
  Popover,
  Button as RACButton,
  SelectValue, Select,
} from 'react-aria-components';

import {Calendar as CalenderIcon, ChevronDown, Plus, X} from 'lucide-react';

import {Label, TextArea} from '@/components/ui/Field.tsx';
import {Button} from '@/components/ui/button.tsx';
import {Calendar} from '@/components/ui/calendar.tsx';
import {SelectFormField, TextFormField} from '@/components/ui/form';
import {FieldArray} from '@/components/ui/form/FieldArray/FieldArray.tsx';
import {FieldArrayItems} from '@/components/ui/form/FieldArray/FieldArrayItems.tsx';
import {FieldError} from '@/components/ui/form/FieldError.tsx';
import {DateFormField} from '@/components/ui/form/fields/DateFormField.tsx';
import {useQuery} from "@apollo/client";
import {getUnitsList} from "@/graphql/lists.ts";
import {useState} from "react";

function EditProcedureForStudyItem({procedures}: {procedures: any[]}) {

  const {data: unitsData} = useQuery(getUnitsList);
  const [selectedUnit, setSelectedUnit] = useState(unitsData?.list_units[0]?.id ?? undefined);
  const [selectedProcedures, setSelectedProcedures] = useState<any[]>([]);

  return (
    <div>
      <Label>Unit</Label>
      <Select
        aria-label="Departments"
        defaultSelectedKey={selectedUnit}
        onSelectionChange={(s: any) => {
          setSelectedUnit(s);
          setSelectedProcedures(procedures.filter((p) => p.unit_id === s));
        }}
      >
        <RACButton className="react-aria-Button h-9 w-50 rounded-sm">
          <SelectValue className="react-aria-SelectValue text-xs text-neutral-900 capitalize" />
          <ChevronDown />
        </RACButton>
        <Popover>
          <ListBox items={unitsData?.list_units}>
            {(unit) => (
              <ListBoxItem
                className="react-aria-ListBoxItem text-sm"
                id={unit.id}
                textValue={String(unit.id)}
              >
                {unit.description}
              </ListBoxItem>
            )}
          </ListBox>
        </Popover>
      </Select>

      <FieldArray
        name="procedures"
        initialItemsCount={1}
      >
        {({append}) => (
          <>
            <FieldArrayItems>
              {({index}) => (
                <div className="border-brand-100 my-4 grid grid-cols-2 gap-4 rounded border p-4">
                  <SelectFormField
                    name={`procedures.${index}.procedure_id`}
                    className="react-aria-Select"
                    required
                  >
                    <Label className="text-neutral-700">Procedure</Label>
                    <RACButton className="react-aria-Button w-full rounded-sm">
                      <SelectValue className="react-aria-SelectValue text-sm" />
                      <ChevronDown />
                    </RACButton>
                    <Popover>
                      <ListBox items={selectedProcedures ?? ([] as any)}>
                        {(item: any) => (
                          <ListBoxItem
                            id={item?.id}
                            textValue={`${item.seq_number}. ${item.name}`}
                          >
                            <div className="overflow-hidden text-ellipsis whitespace-nowrap">
                              {item.seq_number as number}. {item.name}
                            </div>
                          </ListBoxItem>
                        )}
                      </ListBox>
                    </Popover>
                  </SelectFormField>

                  <DateFormField
                    name={`procedures.${index}.provisional_date`}
                    granularity="day"
                    required
                  >
                    <Label className="text-neutral-700">Provisional Date</Label>
                    <Group>
                      <DateInput>{(segment) => <DateSegment segment={segment} />}</DateInput>
                      <RACButton className="react-aria-Button">
                        <CalenderIcon
                          className="h-4 w-4"
                          color="currentColor"
                        />
                      </RACButton>
                    </Group>
                    <Popover className="react-aria-Popover w-max">
                      <Dialog className="p-4">
                        <Calendar />
                      </Dialog>
                    </Popover>
                    <FieldError />
                  </DateFormField>

                  <TextFormField
                    name={`procedures.${index}.note`}
                    className="react-aria-TextField col-span-2"
                  >
                    <Label className="text-neutral-700">Test/Clinical Notes</Label>
                    <TextArea size="md" />
                  </TextFormField>
                  <div>
                    <Button
                      className="p-0"
                      variant="plain"
                      color="danger"
                      size="small"
                      slot="remove"
                    >
                      <X data-slot="icon" />
                      Remove
                    </Button>
                  </div>
                </div>
              )}
            </FieldArrayItems>
            <div className="my-4">
              <Button
                variant="plain"
                color="brand"
                size="small"
                onPress={() => append({})}
              >
                <Plus data-slot="icon" />
                Add Procedure
              </Button>
            </div>
          </>
        )}
      </FieldArray>
    </div>
  );
}

export default EditProcedureForStudyItem;
