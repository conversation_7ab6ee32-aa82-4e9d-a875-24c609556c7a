import clsx from 'clsx';
import {ComponentProps, Key} from 'react';
import {
  Calendar,
  ComboBox,
  DatePicker,
  DateValue,
  Dialog,
  Input,
  ListBox,
  ListBoxItem,
  Popover,
  Button as RAC<PERSON>utton,
  Select,
  SelectValue,
} from 'react-aria-components';

import {fromDate, getLocalTimeZone, toCalendarDateTime} from '@internationalized/date';
import {Calendar as CalenderIcon, Check, ChevronDown} from 'lucide-react';

import {DateTimeSelector} from '@/components/ui/date-time-selector.tsx';

export interface SelectableItem {
  id: string | number;
  value: string;
}

export interface BaseAdaptiveFieldProps {
  type: 'text' | 'number' | 'date' | 'select' | 'combobox';
  setValue: (value: string) => void;
  onKeyDown?: (e: any) => void | undefined;
  setValidationError?: (error: string | undefined) => void;
  validateFn?: (value: string) => string | undefined;
  label?: string;
  className?: string;
}

export interface TextOrNumberProps extends BaseAdaptiveFieldProps {
  type: 'text' | 'number';
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  value: string;
}

export interface SelectProps extends BaseAdaptiveFieldProps {
  type: 'select';
  selectableItems: SelectableItem[];
  onChange?: (e: string) => void;
  value: string;
}

export type ComboBoxProps = Omit<
  ComponentProps<typeof ComboBox>,
  'onSelectionChange' | 'children' | 'slot' | 'validate'
> &
  BaseAdaptiveFieldProps & {
    type: 'combobox';
    onChange?: (e: string) => void;
    handleChange?: (e: string) => void;
    placeholder?: string;
    itemIdKey: string;
    itemValueKey: string;
    itemValueFormatter?: (item: any) => string;
  };

export interface DateProps extends BaseAdaptiveFieldProps {
  type: 'date';
  granularity?: 'day' | 'hour' | 'minute' | 'second';
  hideCalendarButton?: boolean;
  onChange?: (dateValue: DateValue | null) => void;
  value: any;
}

type AdaptiveFieldProps = (TextOrNumberProps | SelectProps | DateProps | ComboBoxProps) &
  Omit<ComponentProps<typeof Input>, 'type' | 'value' | 'onChange'> &
  Omit<ComponentProps<typeof Select>, 'onSelectionChange'> &
  Omit<ComponentProps<typeof DatePicker>, 'onChange' | 'defaultValue' | 'granularity'>;

function AdaptiveField({
  type,
  value,
  setValue,
  onKeyDown,
  onChange,
  setValidationError,
  validateFn,
  label,
  ...rest
}: AdaptiveFieldProps) {
  // const comboboxList = useListData({
  //   items: (rest as ComboBoxProps).comboBoxItems,
  // });

  return (
    <>
      {(() => {
        if (type === 'text' || type === 'number') {
          return (
            <Input
              type={type}
              className={clsx(
                'focus-visible:outline-brand-500 grow rounded-sm p-1 px-2 py-1 text-xs focus:border-0 focus:ring-0 focus-visible:outline-1',
                rest.className
              )}
              value={value}
              onChange={(e) => {
                if (onChange) onChange(e);
                setValue(e.target.value);
              }}
              onKeyDown={onKeyDown}
              aria-label={label}
              onBlur={(e) => {
                const error = validateFn ? validateFn(e.target.value) : undefined;
                if (error && setValidationError) {
                  setValidationError(error);
                }
              }}
              autoFocus
              {...rest}
            />
          );
        }

        if (type === 'select') {
          const {selectableItems, className, setValue, onChange, ...selectRest} = rest as SelectProps;

          console.log('defaultValue: ', value);

          return (
            <Select
              className={clsx('react-aria-Select flex grow items-center justify-between', className)}
              defaultSelectedKey={value}
              onSelectionChange={(key: Key) => {
                const selectedItem = selectableItems.find((item) => item.id === key)?.value;
                setValue(selectedItem ?? '');
                onChange?.(key?.toString() ?? '');
              }}
              aria-label={label}
              onKeyDown={onKeyDown}
              {...selectRest}
            >
              <RACButton
                autoFocus={true}
                className="react-aria-Button w-full p-0.5 px-3"
              >
                <SelectValue className="react-aria-SelectValue w-full text-sm" />
                <ChevronDown />
              </RACButton>
              <Popover className="react-aria-Popover min-w-65 truncate overflow-auto">
                <ListBox items={selectableItems}>
                  {(item: SelectableItem) => (
                    <ListBoxItem
                      id={item.id}
                      textValue={item.value}
                    >
                      <div className="flex w-full cursor-pointer items-center justify-between text-xs">
                        <span className="w-40 truncate">{item.value}</span>
                        <Check className="text-white-white hidden h-4 w-4 [[data-selected=true]_&]:block" />
                      </div>
                    </ListBoxItem>
                  )}
                </ListBox>
              </Popover>
            </Select>
          );
        }

        if (type === 'date') {
          const {granularity = 'day', hideCalendarButton, className, ...dateRest} = rest as DateProps;
          return (
            <DatePicker
              autoFocus={true}
              granularity={granularity as any}
              hourCycle={24}
              shouldForceLeadingZeros
              className="react-aria-DatePicker grow py-0"
              defaultValue={toCalendarDateTime(
                fromDate(value ? new Date(value) : new Date(), getLocalTimeZone())
              )}
              onChange={(dateTime) => {
                if (dateTime) {
                  const date = dateTime.toString();
                  setValue(dateTime as any);
                  onChange?.(date as any);
                }
              }}
              aria-label={label}
              {...dateRest}
            >
              <DateTimeSelector className="react-aria-Group w-full rounded-sm bg-white py-0 text-xs">
                <RACButton className={clsx('react-aria-Button', hideCalendarButton ? 'hidden' : '')}>
                  <CalenderIcon
                    className="h-4 w-4"
                    color="currentColor"
                  />
                </RACButton>
              </DateTimeSelector>
              <Popover className="react-aria-Popover w-max">
                <Dialog>
                  <Calendar />
                </Dialog>
              </Popover>
            </DatePicker>
          );
        }

        if (type === 'combobox') {
          const {
            className,
            onChange,
            placeholder,
            items,
            itemIdKey,
            itemValueFormatter,
            itemValueKey,
            handleChange,
            ...selectRest
          } = rest as ComboBoxProps;
          return (
            <ComboBox
              className={clsx('react-aria-ComboBox w-full', className)}
              items={items}
              autoFocus
              onSelectionChange={(key) => {
                const selected = items && (items as any).find((item: any) => item[itemIdKey] === key);
                if (itemValueFormatter) {
                  setValue(itemValueFormatter(selected));
                } else {
                  setValue(selected?.[itemValueKey] ?? '');
                }
                handleChange?.(selected?.[itemIdKey] ?? '');
              }}
              {...selectRest}
            >
              <div className="relative">
                <Input
                  className="react-aria-Input h-6.5 w-full cursor-text text-xs capitalize"
                  placeholder={placeholder ?? ''}
                />
                <RACButton className="absolute inset-y-0 right-2 z-20 flex cursor-pointer items-center pl-1">
                  <ChevronDown className="size-3" />
                </RACButton>
              </div>
              <Popover>
                <ListBox>
                  {(item: any) => {
                    const value = itemValueFormatter ? itemValueFormatter(item) : item[itemValueKey];
                    return <ListBoxItem id={item[itemIdKey]}>{value}</ListBoxItem>;
                  }}
                </ListBox>
              </Popover>
            </ComboBox>
          );
        }

        return (
          <Input
            type={type}
            className={clsx('grow p-1 text-xs', rest.className)}
            value={value}
            onChange={(e) => {
              if (onChange) onChange(e);
              setValue(e.target.value);
            }}
            onKeyDown={onKeyDown}
            aria-label={label}
            {...rest}
          />
        );
      })()}
    </>
  );
}

export default AdaptiveField;
