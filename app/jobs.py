import logging

from app import scheduler
from app.interface.process import process_interface_queue
from app.message_queue.process import delete_old_completed_outgoing_messages, delete_old_completed_incoming_messages


scheduler.remove_all_jobs()


def interval_job(seconds=30):
    """
    Decorator to register a function as an interval job.
    The job ID is derived from the function name.
    """
    def decorator(func):
        scheduler.add_job(func=func, id=func.__name__, trigger='interval', seconds=seconds, coalesce=True, max_instances=1, misfire_grace_time=60, replace_existing=True)
        return func
    return decorator


def cron_job(hour, minute):
    """
    Decorator to register a function as a cron job.
    The job ID is derived from the function name.
    """
    def decorator(func):
        scheduler.add_job(func=func, id=func.__name__, trigger='cron', hour=hour, minute=minute, coalesce=True, max_instances=1, misfire_grace_time=3600, replace_existing=True)
        return func
    return decorator


##############

@interval_job()
def do_job_1():
    """Sample job 1."""
    logging.info('Job 1 executed')

@interval_job()
def schedule_process_inbound_faxes():
    """
    Schedules the job to process inbound faxes from the message queue.
    """
    with scheduler.app.app_context():
        from app.fax.process import process_inbound_faxes
        process_inbound_faxes()

@interval_job()
def schedule_send_pending_faxes():
    """
    Schedules the job to send any faxes marked as pending.
    """
    with scheduler.app.app_context():
        from app.fax.process import send_pending_faxes
        send_pending_faxes()

# We have a 30 second timeout on the call to the gofax URL. It might helpful to have 2 instances running?
@interval_job()
def schedule_update_fax_statuses():
    """
    Schedules the job to poll GoFax for status updates on sent faxes.
    """
    with scheduler.app.app_context():
        from app.fax.process import update_fax_statuses
        update_fax_statuses()

@interval_job()
def schedule_process_interface_queue():
    """
    Schedules the job to process items from the interface queue.
    """
    with scheduler.app.app_context():
        process_interface_queue()

@interval_job()
def schedule_process_manual_dispatch_messages():
    """
    Schedules the job to process manual dispatch messages from the outgoing message queue.
    """
    with scheduler.app.app_context():
        from app.dispatch.process import process_manual_dispatch_message
        process_manual_dispatch_message()

@cron_job(hour=3, minute=0)
def schedule_delete_old_completed_outgoing_messages():
    """
    Schedules a daily job to delete completed messages from the outgoing message
    queue that were last updated more than 7 days ago.
    """
    with scheduler.app.app_context():
        delete_old_completed_outgoing_messages()

@cron_job(hour=3, minute=5)
def schedule_delete_old_completed_incoming_messages():
    """
    Schedules a daily job to delete completed messages from the incoming message
    queue that were last updated more than 7 days ago.
    """
    with scheduler.app.app_context():
        delete_old_completed_incoming_messages()
