from app import db
from app.dispatch.models import DispatchRecord, DispatchStatusLog
from app.site.admin_mixins import SiteSpecificAdminMixin
from sqlalchemy.orm import aliased
from sqlalchemy.sql import func


class DispatchRecordView(SiteSpecificAdminMixin):
    column_list = [
        'id', 'message_id', 'file_name', 'recipient_fax', 'provider_number', 'client', 'test_type',
        'dispatch_time_utc', 'current_status', 'current_severity', 'current_category', 'status_timestamp'
    ]

    column_details_list = [
        'id', 'message_id', 'file_name', 'recipient_fax', 'provider_number', 'client', 'test_type',
        'dispatch_time_utc', 'current_status', 'current_severity', 'current_category', 'status_timestamp',
        'data_base64', 'record_id', 'ur'
    ]

    column_default_sort = ('dispatch_time_utc', True)

    def get_query(self):
        # Subquery to get the latest status log per message_id
        latest_status_subquery = db.session.query(
            DispatchStatusLog.message_id,
            func.max(DispatchStatusLog.timestamp).label('max_timestamp')
        ).group_by(DispatchStatusLog.message_id).subquery()

        # Join the DispatchRecord with the latest DispatchStatusLog per message_id
        latest_status = aliased(DispatchStatusLog)

        query = db.session.query(
            DispatchRecord,
            latest_status.severity.label('current_severity'),
            latest_status.message_category.label('current_category'),
            latest_status.text.label('current_status'),
            latest_status.timestamp.label('status_timestamp')
        ).outerjoin(
            latest_status_subquery,
            DispatchRecord.message_id == latest_status_subquery.c.message_id
        ).outerjoin(
            latest_status,
            (DispatchRecord.message_id == latest_status.message_id) &
            (latest_status.timestamp == latest_status_subquery.c.max_timestamp)
        )

        return query

    def get_count_query(self):
        return db.session.query(func.count('*')).select_from(DispatchRecord)