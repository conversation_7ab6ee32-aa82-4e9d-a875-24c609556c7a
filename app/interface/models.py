from datetime import datetime
from app import db
from sqlalchemy.orm import relationship
from app.pdf_convert.models import PasPt

class InterfaceReport(db.Model):
    __tablename__ = 'interface_report'
    interface_report_id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    report_content = db.Column(db.Text, nullable=False)


class InterfaceQueue(db.Model):
    __tablename__ = 'interface_queue'
    interface_queue_id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    interface_id = db.Column(db.Integer, nullable=False)
    test_code = db.Column(db.Integer, nullable=False)
    event_type = db.Column(db.Integer, nullable=False)
    record_id = db.Column(db.Integer, nullable=False)
    patient_id = db.Column(db.Integer, db.<PERSON>ey('pas_pt.patientid'), nullable=False)
    message = db.Column(db.Text)
    processed = db.Column(db.Integer, nullable=False)
    failure_reason = db.Column(db.String(2000))
    msg_ctrl_id = db.Column(db.String(128))
    event_date_time = db.Column(db.DateTime(timezone=False), nullable=False)
    message_constructed = db.Column(db.DateTime(timezone=False))
    message_sent = db.Column(db.DateTime(timezone=False))
    message_type = db.Column(db.String(6))
    trigger_event = db.Column(db.String(6))
    study_object = db.Column(db.Text)
    interface_report_id = db.Column(db.Integer, db.ForeignKey('interface_report.interface_report_id'))
    parameters = db.Column(db.String(255))
    interface_configuration_id = db.Column(db.Integer, nullable=False)
    interface_configuration_condition_id = db.Column(db.Integer)
    interface_configuration_iterator_id = db.Column(db.Integer)

    report = relationship("InterfaceReport")
    patient = relationship("PasPt")

    def to_dict(self):
        d = {}
        for c in self.__table__.columns:
            val = getattr(self, c.name)
            if isinstance(val, datetime):
                d[c.name] = val.isoformat()
            else:
                d[c.name] = val
        return d