import logging
from datetime import datetime, timedelta

from app import db
from app.message_queue.models import IncomingMessageQueue, OutgoingMessageQueue


def _delete_old_completed_messages(model, queue_type_name: str):
    """
    Deletes completed messages from a given message queue model that were last updated more than 7 days ago.

    :param model: The SQLAlchemy model class for the message queue.
    :param queue_type_name: A string representing the queue type for logging purposes (e.g., 'outgoing', 'incoming').
    """
    job_name = f'delete_old_completed_{queue_type_name}_messages'
    logging.info(f'Starting job: {job_name}')
    try:
        seven_days_ago = datetime.utcnow() - timedelta(days=7)

        messages_deleted_count = model.query.filter(
            model.status == 'completed',
            model.updated_at < seven_days_ago
        ).delete(synchronize_session=False)

        db.session.commit()
        logging.info(f'Successfully deleted {messages_deleted_count} old completed {queue_type_name} messages.')

    except Exception as e:
        db.session.rollback()
        logging.exception(f"Error while deleting old {queue_type_name} messages: {e}")
    finally:
        logging.info(f'Finished job: {job_name}')


def delete_old_completed_outgoing_messages():
    """Deletes completed messages from the outgoing message queue that were last updated more than 7 days ago."""
    _delete_old_completed_messages(OutgoingMessageQueue, 'outgoing')


def delete_old_completed_incoming_messages():
    """Deletes completed messages from the incoming message queue that were last updated more than 7 days ago."""
    _delete_old_completed_messages(IncomingMessageQueue, 'incoming')
