import json

from sqlalchemy import Index, ForeignKey, func
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import declared_attr
from sqlalchemy.types import BigInteger, Text
from sqlalchemy_utils import ChoiceType

from app import db


INCOMING_MESSAGE_STATUS_OPTIONS = [
    ('pending', 'Pending'),
    ('processing', 'Processing'),
    ('failed', 'Failed'),
    ('completed', 'Completed')
]


OUTGOING_MESSAGE_STATUS_OPTIONS = [
    ('pending', 'Pending'),
    ('awaiting_ack', 'Awaiting Ack'),
    ('failed', 'Failed'),
    ('completed', 'Completed')
]


class MessageQueueBase:
    id = db.Column(BigInteger, primary_key=True, autoincrement=True)
    site_id = db.Column(BigInteger, ForeignKey('site.id'), nullable=False)
    message_id = db.Column(Text, nullable=False)
    payload = db.Column(JSONB, nullable=False)
    queue_name = db.Column(Text, nullable=False)
    created_at = db.Column(db.DateTime(timezone=True), nullable=False, server_default=func.now())
    updated_at = db.Column(db.DateTime(timezone=True), nullable=False, server_default=func.now(), onupdate=func.now())

    @declared_attr
    def site(cls):
        return db.relationship('Site')

    @declared_attr
    def __table_args__(cls):
        return (
            db.Index(f'idx_{cls.__tablename__}_status', 'status'),
            db.Index(f'idx_{cls.__tablename__}_name', 'queue_name'),
            db.Index(f'idx_{cls.__tablename__}_site_id', 'site_id'),
            db.Index(f'idx_{cls.__tablename__}_name_status', 'queue_name', 'status'),
        )

    def __repr__(self):
        return f'<{self.__class__.__name__} id={self.id} message_id={self.message_id} site_id={self.site_id} queue="{self.queue_name}" status="{self.status}">'

    def to_dict(self):
        return {
            'id': self.id,
            'message_id': self.message_id,
            'site_id': self.site_id,
            'queue_name': self.queue_name,
            'payload': self.payload,
            'status': self.status.code if self.status else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
        }


class IncomingMessageQueue(MessageQueueBase, db.Model):
    __tablename__ = 'message_incoming_queue'
    status = db.Column(ChoiceType(INCOMING_MESSAGE_STATUS_OPTIONS), nullable=False, default='pending')


class OutgoingMessageQueue(MessageQueueBase, db.Model):
    __tablename__ = 'message_outgoing_queue'
    status = db.Column(ChoiceType(OUTGOING_MESSAGE_STATUS_OPTIONS), nullable=False, default='pending')
    attempts = db.Column(db.Integer, nullable=False, default=0)
    error_message = db.Column(Text, nullable=True)

    def to_dict(self):
        d = super().to_dict()
        d['attempts'] = self.attempts
        d['error_message'] = self.error_message
        return d
