import logging
import time
import json
from datetime import datetime, timedelta, timezone
from functools import wraps

from flask import Blueprint, jsonify, request
from sqlalchemy import text

from app import db, csrf
from app.message_queue.models import IncomingMessageQueue, OutgoingMessageQueue

logger = logging.getLogger(__name__)

message_queue_bp = Blueprint('message_queue_bp', __name__)

API_KEYS = {
    "development-api-key-;ergij;kl23poiugjkls": {
        "name": "Integration Service",
        "site_id": "1",
        "inbound_only": False
    }
}

def require_api_key(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Check for API key in header
        api_key = request.headers.get('X-API-Key')
        
        if not api_key:
            # Also check query parameter as fallback
            api_key = request.args.get('api_key')
        
        key_config = API_KEYS.get(api_key)
        if not key_config:
            return jsonify({'error': 'Invalid or missing API key'}), 401

        # Set attributes from key config on the request object
        request.site_id = key_config['site_id']
        request.inbound_only = key_config.get('inbound_only', True)

        return f(*args, **kwargs)
    
    return decorated_function


#We try to accept most messages because we don't want to be forced to debug on-site.
#(TODO) Currently this allows both incoming and outgoing messages to be posted.
#Later on it should be inbound only.
@message_queue_bp.route('/api/integration/message-queue/create', methods=['POST'])
@csrf.exempt
@require_api_key
def add_to_message_queue():
    data = request.get_json()

    if not data:
        logger.error("Failed to add message to queue: No JSON payload provided. Request data: %s", request.data)
        return jsonify({"error": "No JSON payload provided"}), 400

    required_fields = ['payload']
    missing_fields = [field for field in required_fields if field not in data]
    if missing_fields:
        error_msg = f"Missing required fields: {', '.join(missing_fields)}"
        logger.error("Failed to add message to queue: %s. Data: %s", error_msg, data)
        return jsonify({"error": error_msg}), 400

    site_id = request.site_id

    queue_name = data.get('queue_name')
    payload_data = data.get('payload')
    message_id = data.get('message_id')
    direction = data.get('direction', 'incoming')

    if direction not in ['incoming', 'outgoing']:
        error_msg = "Invalid direction. Must be 'incoming' or 'outgoing'"
        logger.error("Failed to add message to queue: %s. Data: %s", error_msg, data)
        return jsonify({"error": error_msg}), 400

    if direction == 'outgoing':
        if request.inbound_only:
            error_msg = "This API key is configured for inbound messages only."
            logger.error("Failed to add message to queue: %s. Data: %s", error_msg, data)
            return jsonify({"error": error_msg}), 403

        if not queue_name:
            error_msg = "queue_name is mandatory for outgoing messages"
            logger.error("Failed to add message to queue: %s. Data: %s", error_msg, data)
            return jsonify({"error": error_msg}), 400
        if not message_id:
            error_msg = "message_id is mandatory for outgoing messages"
            logger.error("Failed to add message to queue: %s. Data: %s", error_msg, data)
            return jsonify({"error": error_msg}), 400

    try:
        if direction == 'incoming':
            message = IncomingMessageQueue(
                site_id=site_id,
                queue_name=queue_name,
                payload=payload_data,
                message_id=message_id
            )
        else:  # outgoing
            message = OutgoingMessageQueue(
                site_id=site_id,
                queue_name=queue_name,
                payload=payload_data,
                message_id=message_id
            )

        db.session.add(message)
        db.session.commit()
        return jsonify([]), 201
    except Exception as e:
        logger.error(f"Error adding to message queue: {str(e)}. Data: {data}", exc_info=True)
        return jsonify({"error": "Failed to add message to queue. See server logs for details."}), 500


@message_queue_bp.route('/api/integration/message-queue/poll', methods=['POST'])
@csrf.exempt
@require_api_key
def poll_message_queue():
    site_id = request.site_id
    timeout = 30  # seconds

    start_time = time.time()
    while time.time() - start_time < timeout:
        # Using a raw SQL query to get a batch of messages up to a cumulative size of 5MB.
        # It first locks candidate messages, then uses a window function to calculate cumulative size.
        # This avoids a 'FOR UPDATE is not allowed with window functions' error.
        # If the first message is > 5MB, it's sent by itself.
        #
        # The backoff schedule for retries is as follows:
        # `attempts` is the value in the DB for a message that has been picked up by the poller.
        # A message with 0 attempts is picked up immediately.
        # For messages with > 0 attempts, the wait time is how long must pass since `created_at` before a retry.
        # attempts | wait time
        # ---------|----------------------
        # 1        | 2.5 mins
        # 2        | 3.75 mins
        # 3        | 5.6 mins
        # 4        | 8.4 mins
        # 5        | 12.7 mins
        # 6        | 19.0 mins
        # 7        | 28.5 mins
        # 8        | 42.7 mins
        # 9        | 1.07 hours
        # 10       | 1.6 hours
        # 11       | 2.4 hours
        # 12       | 3.6 hours
        # 13       | 5.4 hours
        # 14       | 8.1 hours
        # 15       | 12.2 hours
        # 16       | 18.2 hours
        # 17       | 27.4 hours
        # 18       | 41.1 hours
        # 19+      | 48 hours (capped)
        #
        # A message is moved to the 'failed' state if it remains unacknowledged for more than 3 days.
        query = text("""
            WITH locked_candidates AS (
                SELECT id, payload, created_at, status as previous_status
                FROM message_outgoing_queue
                WHERE site_id = :site_id AND
                (status = 'pending' OR status = 'awaiting_ack') AND
                (
                    (attempts = 0 AND status = 'pending') OR
                    -- Exponential backoff for retries. 2.5min base, capped at 2 days.
                    created_at < NOW() - (LEAST(150 * POWER(1.5, attempts - 1), 172800) * INTERVAL '1 second')
                )
                ORDER BY created_at ASC
                FOR UPDATE SKIP LOCKED
            ),
            ranked_candidates AS (
                SELECT
                    id,
                    previous_status,
                    ROW_NUMBER() OVER (ORDER BY created_at ASC) as rn,
                    octet_length(payload::text) as size,
                    SUM(octet_length(payload::text)) OVER (ORDER BY created_at ASC) as cumulative_size
                FROM locked_candidates
            ),
            messages_to_process AS (
                SELECT id, previous_status
                FROM ranked_candidates
                WHERE cumulative_size <= 5242880 -- 5MB limit
                   OR rn = 1
            )
            UPDATE message_outgoing_queue m
            SET status = 'awaiting_ack', attempts = m.attempts + 1
            FROM messages_to_process mtp
            WHERE m.id = mtp.id
            RETURNING m.id, m.message_id, m.site_id, m.queue_name, m.payload, m.status, m.created_at, m.updated_at, m.attempts, m.error_message, octet_length(m.payload::text) as size, mtp.previous_status;
        """)

        try:
            result = db.session.execute(query, {"site_id": site_id})
            messages = result.fetchall()
            db.session.commit()  # Commit the transaction to make the status change permanent
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error polling message queue: {str(e)}", exc_info=True)
            # In case of error, wait a bit and try again within the timeout
            time.sleep(1)
            continue

        if messages:
            valid_messages = []
            invalid_messages_ids_and_errors = []
            for msg in messages:
                error = None
                # Fail messages that are older than 3 days.
                if datetime.now(timezone.utc) - msg.created_at > timedelta(days=3):
                    error = f'Exceeded retry time limit (3 days). Previous state: {msg.previous_status}'
                else:
                    # For JSONB, the payload is already a dict, so no need to parse.
                    # The check is implicitly handled by the database.
                    pass

                if error:
                    invalid_messages_ids_and_errors.append({'id': msg.id, 'error': error})
                else:
                    valid_messages.append(msg)

            if invalid_messages_ids_and_errors:
                try:
                    for item in invalid_messages_ids_and_errors:
                        db.session.query(OutgoingMessageQueue).filter_by(id=item['id']).update({
                            'status': 'failed',
                            'error_message': item['error']
                        })
                    db.session.commit()
                except Exception as e:
                    db.session.rollback()
                    logger.error(f"Error updating invalid messages to failed status: {str(e)}", exc_info=True)

            if not valid_messages:
                time.sleep(1)
                continue

            messages_list = [
                {
                    'id': msg.id,
                    'message_id': msg.message_id,
                    'queue_name': msg.queue_name,
                    'payload': msg.payload,
                } for msg in valid_messages
            ]
            
            return jsonify(messages_list)

        time.sleep(1)  # sleep for 1 second before checking again

    return jsonify([]), 200


@message_queue_bp.route('/api/integration/message-queue/ack', methods=['POST'])
@csrf.exempt
@require_api_key
def ack_message():
    """
    Acknowledge one or more outgoing messages to mark them as 'completed'.
    This endpoint accepts a list of message IDs (the client-provided string ID).

    Example payload:
    {
        "message_ids": ["client-id-1", "client-id-2"]
    }
    """
    data = request.get_json()
    site_id = request.site_id

    if not data or 'message_ids' not in data:
        return jsonify({"error": "message_ids is required"}), 200

    message_ids = data.get('message_ids')
    if not isinstance(message_ids, list):
        return jsonify({"error": "message_ids must be a list"}), 200

    if not message_ids:
        return jsonify({"message": "No message IDs provided"}), 200

    unique_message_ids = set(message_ids)

    messages = OutgoingMessageQueue.query.filter(
        OutgoingMessageQueue.message_id.in_(unique_message_ids),
        OutgoingMessageQueue.site_id == site_id
    ).all()

    if len(messages) != len(unique_message_ids):
        found_ids = {msg.message_id for msg in messages}
        not_found_ids = list(unique_message_ids - found_ids)
        logger.warning(f"Attempted to ack non-existent or unauthorized messages for site {site_id}: {not_found_ids}")

    try:
        for message in messages:
            if message.status != 'awaiting_ack':
                logger.warning(
                    f"Attempted to ack message {message.id} which is not in 'awaiting_ack' state. Current state: {message.status}")
            message.status = 'completed'

        db.session.commit()
        return jsonify({"message": "Messages acknowledged successfully"}), 200
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error acknowledging messages {message_ids}: {str(e)}", exc_info=True)
        return jsonify({"error": "Failed to acknowledge messages. See server logs for details."}), 500

