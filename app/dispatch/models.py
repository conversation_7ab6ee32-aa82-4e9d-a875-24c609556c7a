from app import db
from sqlalchemy.dialects.postgresql import UUID
from app.site.models import Site


class DispatchRecord(db.Model):
    __tablename__ = 'dispatch_record'

    id = db.Column(db.BigInteger, primary_key=True)
    message_id = db.Column(db.String(255), unique=True, nullable=False)

    source = db.Column(db.String(50), nullable=False)
    status = db.Column(db.String(50), nullable=False)
    stage = db.Column(db.String(50))
    failure_reason = db.Column(db.Text)
    is_resolved = db.Column(db.Boolean, server_default='f')
    notes = db.Column(db.Text)
    attempts = db.Column(db.Integer, server_default='0')

    created_at = db.Column(db.DateTime, nullable=False, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, nullable=False, server_default=db.func.now(), onupdate=db.func.now())

    # Dispatch Metadata (multi-channel delivery targets)
    filename = db.Column(db.String(255))
    base64_data = db.Column(db.Text)
    edi = db.Column(db.String(50))
    provider_number = db.Column(db.String(50))
    fax_number = db.Column(db.String(50))

    # GoFax integration (optional linkage)
    gofax_outbound_id = db.Column(UUID(as_uuid=True))

    # HL7 acknowledgment details
    ack_code = db.Column(db.String(10))
    ack_message = db.Column(db.String(1000))

    # Clinical & Patient Metadata
    record_id = db.Column(db.String(255))
    ur = db.Column(db.String(50))
    site_id = db.Column(db.Integer, db.ForeignKey('site.id'), nullable=False)
    site = db.relationship('Site')
    user_id = db.Column(db.String(255))

    # Optional link to interface queue/log (nullable for non-HL7 dispatches)
    interface_queue_id = db.Column(db.Integer, db.ForeignKey('interface_queue.interface_queue_id'))
    interface_log_id = db.Column(db.Integer, db.ForeignKey('interface_log.interface_log_id'))

    def __repr__(self):
        return f'<DispatchRecord {self.message_id}>'


class DispatchStatusLog(db.Model):
    __tablename__ = 'dispatch_status_log'

    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    message_id = db.Column(db.Text, nullable=False)  # References the original dispatch message
    correlation_id = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime(timezone=True), nullable=False, server_default=db.func.now())  # TIMESTAMPTZ
    updated_at = db.Column(db.DateTime(timezone=True), nullable=False, server_default=db.func.now(), onupdate=db.func.now())
    producer_id = db.Column(db.Text, nullable=True)
    severity = db.Column(db.Text, nullable=False)
    message_category = db.Column(db.Text, nullable=False)
    text = db.Column(db.Text, nullable=False)
    site_id = db.Column(db.Integer, db.ForeignKey('site.id'), nullable=False)

    def __repr__(self):
        return f'<DispatchStatusLog id={self.id}, message_id={self.message_id}, severity={self.severity}>'
