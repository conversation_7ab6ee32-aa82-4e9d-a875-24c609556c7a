import json
import logging
from datetime import datetime

from app import db
from app.dispatch.models import Dispatch<PERSON>ecord
from app.message_queue.models import OutgoingMessageQueue,IncomingMessageQueue

log = logging.getLogger(__name__)


def process_manual_dispatch_message():
    """
    Processes messages from the outgoing queue with queue_name 'manual_dispatch_message' and moves them
    to the dispatch_record table.
    """
    log.info("Starting process_manual_dispatch_message job.")

    for _ in range(100):
        # Lock and retrieve the next available message.
        message = db.session.query(IncomingMessageQueue).filter_by(
            queue_name='manual_dispatch_message',
            status='pending'
        ).order_by(IncomingMessageQueue.created_at.asc()).with_for_update(skip_locked=True).first()

        if not message:
            break

        try:
            data = message.payload
            if isinstance(data, str):
                data = json.loads(data)

            if DispatchRecord.query.filter_by(message_id=data['MessageId']).first():
                log.warning(f"Dispatch record for message {data['MessageId']} already exists. Skipping.")
                message.status = 'completed'
                db.session.add(message)
                db.session.commit()
                continue

            record = DispatchRecord(
                message_id=data['MessageId'],
                site_id=message.site_id,
                source='manual',
                status='pending',
                base64_data=data['DataBase64'],
                filename=data['FileName'],
                edi=data.get('RecipientEDI'),
                provider_number=data.get('ProviderNumber'),
                fax_number=data.get('RecipientFax'),
                record_id=data.get('RecordId'),
                ur=data.get('UR')
            )
            db.session.add(record)

            message.status = 'completed'
            db.session.add(message)
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            log.exception(f"Failed to process message {message.id}: {e}")
            try:
                # After rollback, message is detached. We need to update it in a new transaction.
                db.session.query(IncomingMessageQueue).filter_by(id=message.id,  status='pending').update({
                    'status': 'failed'
                })
                db.session.commit()
            except Exception as e2:
                log.exception(f"Could not mark message {message.id} as failed. Error: {e2}")
                db.session.rollback()

    log.info("Finished process_manual_dispatch_message job.")
