from flask import redirect, url_for, flash, request, session
from flask_admin.contrib.sqla import ModelView
from flask_login import current_user, logout_user
from sqlalchemy.orm import Query

class AdminSecurityMixin:
    def is_accessible(self):
        return (
            current_user.is_authenticated and
            current_user.is_active and
            current_user.has_role("superuser")
        )

    def _handle_view(self, name, **kwargs):
        if not self.is_accessible():
            if current_user.is_authenticated:
                flash("You need administrator privileges to access this page")
                logout_user()
            return redirect(url_for("security.login", next=request.url))

class SiteSpecificAdminMixin(AdminSecurityMixin, ModelView):
    def col_style(self, col_name):
        return ''

    site_id_field = 'site_id'
    set_site_id = True

    def get_current_site_id(self):
        return session.get('site_id')

    def site_filter(self):
        return self.model.site_id == self.get_current_site_id()

    def get_query(self):
        filter = self.site_filter()
        if filter is not None:
            return super().get_query().filter(self.site_filter())
        return super().get_query()

    def get_count_query(self):
        filter = self.site_filter()
        if filter is not None:
            return super().get_count_query().filter(self.site_filter())
        return super().get_count_query()

    def on_model_change(self, form, model, is_created):
        if self.site_id_field and self.set_site_id:
            setattr(model, self.site_id_field, self.get_current_site_id())
        super().on_model_change(form, model, is_created)


class GlobalSiteAdminMixin(AdminSecurityMixin, ModelView):
    def col_style(self, col_name):
        return ''

    def get_current_site_id(self):
        return session.get('site_id')

    def is_accessible(self):
        return self.get_current_site_id() == 1

    def get_query(self):
        site_id = self.get_current_site_id()
        if site_id == 1:
            return super().get_query()
        return None

    def get_count_query(self):
        site_id = self.get_current_site_id()
        if site_id == 1:
            return super().get_count_query()
        return None
