from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app import db


class StudyRequest(db.Model):
    __tablename__ = 'study_request'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    pdf_file_url = db.Column(db.String, nullable=True)
    type = db.Column(db.String, nullable=True)
    requesting_doctor_id = db.Column(db.BigInteger, db.<PERSON>ey('doctors.id'), nullable=True)
    report_cc_id = db.Column(db.BigInteger, db.<PERSON>ey('doctors.id'), nullable=True)
    patient_id = db.Column(db.BigInteger, db.<PERSON>ey('pas_pt.patientid'), nullable=True)
    site_id = db.Column(db.BigInteger, db.ForeignKey('site.id'), nullable=True)
    urgency = db.Column(db.String, default='Routine', nullable=True)
    appointment_note = db.Column(db.Text, nullable=True)
    date_recieved = db.Column(db.DateTime, nullable=False, default=func.now())
    date_created = db.Column(db.DateTime, nullable=False, default=func.now())
    

class StudyRequestItem(db.Model):
    __tablename__ = 'study_request_item'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    study_request_id = db.Column(db.BigInteger, db.ForeignKey('study_request.id'), nullable=False)
    procedure_id = db.Column(db.BigInteger, db.ForeignKey('procedure.id'), nullable=True)
    status = db.Column(db.String, default='pending', nullable=True)
    rejection_reason = db.Column(db.Text, nullable=True)
    note = db.Column(db.Text, nullable=True)
    actioned_at = db.Column(db.DateTime, nullable=True)
    provisional_procedure_date = db.Column(db.DateTime, nullable=True)