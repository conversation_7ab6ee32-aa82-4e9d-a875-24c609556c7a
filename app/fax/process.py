import json
import logging
import time
from datetime import datetime, timedelta

from app import db
from app.fax.models import FaxOutbound
from app.fax.gofax import GoFax, SendFaxObject

def process_inbound_faxes():
    """
    Job to read messages from the inbound_message_queue table and store them in the database.
    """
    from app.message_queue.models import IncomingMessageQueue

    logging.info("Starting process_inbound_faxes job.")

    for _ in range(100):
        # Lock and retrieve the next available message.
        msg = db.session.query(IncomingMessageQueue).filter_by(status='pending', queue_name='fax_queue').order_by(IncomingMessageQueue.created_at.asc()).with_for_update(skip_locked=True).first()

        if not msg:
            break

        logging.info(f"Processing inbound message {msg.id}")

        try:
            data = msg.payload
            if isinstance(data, str):
                data = json.loads(data)

            # Check if message already processed in fax_outbound
            if db.session.query(FaxOutbound).filter_by(message_id=data.get('MessageId')).first():
                logging.warning(f"Fax request with MessageId {data.get('MessageId')} already exists. Marking message as completed.")
                msg.status = 'completed'
                db.session.commit()
                continue

            fax = FaxOutbound(
                site_id=msg.site_id,
                report_file_content_data=data.get('ReportFileContentData'),
                client=data.get('Client'),
                test_type=data.get('TestType'),
                record_id=data.get('RecordId'),
                ur=data.get('UR'),
                recipient_fax=data.get('RecipientFax'),
                file_content=data.get('FileContent'),
                file_name=data.get('FileName'),
                correlation_id=data.get('CorrelationId'),
                message_id=data.get('MessageId'),
                timestamp=datetime.fromisoformat(data.get('Timestamp').replace('Z', '+00:00')),
                producer_id=data.get('ProducerId'),
                raw_message=data
            )
            db.session.add(fax)
            msg.status = 'completed'
            db.session.commit()
            logging.info(f"Successfully processed message {msg.id} and created fax request with MessageId {fax.message_id}")
        except Exception as e:
            db.session.rollback()
            logging.exception(f"Failed to process message {msg.id}. Error: {e}")
            try:
                # After rollback, msg is detached. We need to update it in a new transaction.
                db.session.query(IncomingMessageQueue).filter_by(id=msg.id).update({'status': 'failed'})
                db.session.commit()
            except Exception as e2:
                logging.exception(f"Could not mark message {msg.id} as failed. Error: {e2}")
                db.session.rollback()

    logging.info("Finished process_inbound_faxes job.")


def send_pending_faxes():
    """
    Job to send faxes that are in the 'pending' state.
    """
    logging.info("send_pending_faxes: Starting job.")
    
    gofax = GoFax()
    
    for _ in range(100):  # Process up to 100 faxes at a time
        # Get one pending fax at a time
        pending_fax = db.session.query(FaxOutbound).filter_by(status='pending').with_for_update(skip_locked=True).first()
        
        if not pending_fax:
            logging.info("send_pending_faxes: No more pending faxes to send.")
            break
            
        logging.info(f"send_pending_faxes: Processing pending fax {pending_fax.id} for sending.")
        
        try:
            if not pending_fax.report_file_content_data:
                raise ValueError("File content is missing.")
            
            #send_from_setting = SiteSettings.get(pending_fax.site_id, 'GOFAX_SENDER')
            #if not send_from_setting or not send_from_setting.value:
            #    raise ValueError(f"GoFax sender number not configured for site {pending_fax.site_id}.")
            
            fax_request = SendFaxObject(
                send_to=pending_fax.recipient_fax,
                send_from='6111111111', # TODO - hardcoded currently.
                filename=pending_fax.file_name,
                file_base64=pending_fax.report_file_content_data,
            )
            
            response = gofax.send_fax(fax_request)
            
            if response.get('success') and response.get('submitted'):
                pending_fax.gofax_id = response.get('fax_id')
                if pending_fax.gofax_id:
                    pending_fax.status = 'processing'
                    pending_fax.gofax_status = 'Submitted'
                    logging.info(f"send_pending_faxes: Fax {pending_fax.id} sent successfully. GoFax ID: {pending_fax.gofax_id}")
                else:
                    pending_fax.status = 'failed'
                    pending_fax.gofax_error = "GoFax API did not return a FaxId."
                    logging.error(f"send_pending_faxes: Failed to send fax {pending_fax.id}: {pending_fax.gofax_error}")
            else:
                pending_fax.status = 'failed'
                pending_fax.gofax_error = response.get('message') or response.get('document_message') or "Unknown error from GoFax"
                logging.error(f"send_pending_faxes: Failed to send fax {pending_fax.id}. Error: {pending_fax.gofax_error}")
            
            db.session.commit()
            
        except Exception as e:
            logging.exception(f"send_pending_faxes: An error occurred while sending fax {pending_fax.id}: {e}")
            pending_fax.status = 'failed'
            pending_fax.gofax_error = str(e)
            db.session.commit()
    
    logging.info("send_pending_faxes: Finished job.")
    

def update_fax_statuses():
    """
    Job to poll GoFax for status updates on sent faxes.
    """
    logging.info("Starting update_fax_statuses job.")
    gofax = GoFax()

    for _ in range(100): # If the API is bad, or the internet is down, we don't want to loop forever.
        time.sleep(2)

        # Lock and retrieve the next available fax.
        # The lock is released upon commit() or rollback().
        fax = db.session.query(FaxOutbound).filter(
            FaxOutbound.status == 'processing',
            FaxOutbound.gofax_id.isnot(None),
            db.or_(
                FaxOutbound.status_attempts <= 1,
                FaxOutbound.updated_at < (datetime.utcnow() - timedelta(minutes=1))
            )
        ).order_by(FaxOutbound.updated_at.asc()).with_for_update(skip_locked=True).first()

        if not fax:
            # No more faxes to process in this run.
            break


        try:
            fax.status_attempts += 1
            logging.info(f"Requesting status for fax {fax.id} (GoFax ID: {fax.gofax_id}, Attempt: {fax.status_attempts})")
            status_response = gofax.get_fax_status(fax.gofax_id)

            # Check if we got a valid response
            if status_response.get('success'):
                if status_response.get('in_progress'):
                    logging.info(f"Fax {fax.id} (GoFax ID: {fax.gofax_id}) still in progress")
                
                elif status_response.get('error'):
                    fax.status = 'error'
                    fax.error_message = status_response.get('message') or "Fax failed"
                    logging.error(f"Fax {fax.id} (GoFax ID: {fax.gofax_id}) failed: {fax.error_message}")
                
                # If it's success, then clear error message, set to complete
                else:
                    fax.status = 'completed'
                    fax.error_message = None
                    logging.info(f"Fax {fax.id} (GoFax ID: {fax.gofax_id}) completed successfully")
                
                logging.info(f"Updated status for fax {fax.id} (GoFax ID: {fax.gofax_id}) to {fax.gofax_status}")
            else:
                error_message = status_response.get('message') or "Unknown error from GoFax"
                fax.error_message = error_message
                logging.warning(f"Could not get status for fax {fax.id} (GoFax ID: {fax.gofax_id}). Error: {error_message}")

            db.session.commit()
        except Exception as e:
            logging.error(f"An error occurred while updating status for fax {fax.id}: {e}")
            db.session.rollback()

    logging.info("Finished update_fax_statuses job.")


