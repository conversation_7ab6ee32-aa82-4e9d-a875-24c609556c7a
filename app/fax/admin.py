from app import db
from app.fax.models import FaxOutbound
from app.site.admin_mixins import SiteSpecificAdminMixin


class FaxOutboundView(SiteSpecificAdminMixin):
    column_list = ['id', 'site', 'recipient_fax', 'file_name', 'status', 'gofax_id', 'gofax_status', 'status_attempts', 'created_at', 'updated_at', 'gofax_error']
    column_details_list = ['id', 'site', 'recipient_fax', 'file_name', 'status', 'gofax_id', 'gofax_status', 'gofax_error', 'status_attempts', 'message_id', 'correlation_id', 'timestamp', 'created_at', 'updated_at', 'raw_message']
    column_default_sort = ('created_at', True)


def add_admins(admin):
    admin.add_view(
        FaxOutboundView(
            FaxOutbound,
            db.session,
            name="Outbound Faxes",
            category="Integration",
            endpoint="outbound_faxes",
        )
    )
