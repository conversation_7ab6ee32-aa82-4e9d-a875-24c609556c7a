import uuid
from sqlalchemy import func
from sqlalchemy.dialects.postgresql import UUID, JSONB
from app.site.models import Site
from app import db


class FaxOutbound(db.Model):
    __tablename__ = 'fax_outbound'

    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    created_at = db.Column(db.DateTime, nullable=False, server_default=func.now())
    updated_at = db.Column(db.DateTime, nullable=False, server_default=func.now())
    site_id = db.Column(db.Integer, db.ForeignKey('site.id'), nullable=True)
    site = db.relationship('Site')

    # Fields from JSON
    report_file_content_data = db.Column(db.Text, nullable=False)
    client = db.Column(db.String, nullable=True)
    test_type = db.Column(db.String, nullable=True)
    record_id = db.Column(db.String, nullable=True)
    ur = db.Column(db.String, nullable=True)
    recipient_fax = db.Column(db.String, nullable=False)
    file_content = db.Column(db.Text)
    file_name = db.Column(db.String)
    correlation_id = db.Column(db.String)
    message_id = db.Column(db.String, unique=True, nullable=False)
    timestamp = db.Column(db.DateTime, nullable=False)
    producer_id = db.Column(db.String)

    # Fax sending status fields
    status = db.Column(db.String, default='pending', nullable=False)
    gofax_id = db.Column(db.Integer, nullable=True)
    gofax_status = db.Column(db.String, nullable=True)
    gofax_error = db.Column(db.Text, nullable=True)
    status_attempts = db.Column(db.Integer, nullable=False, default=0, server_default='0')

    raw_message = db.Column(JSONB, nullable=True)

    def to_dict(self):
        return {
            "id": str(self.id),
            "site_id": self.site_id,
            "status": self.status,
            "gofax_id": self.gofax_id,
            "gofax_status": self.gofax_status,
            "status_attempts": self.status_attempts,
            "recipient_fax": self.recipient_fax,
            "file_name": self.file_name,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
