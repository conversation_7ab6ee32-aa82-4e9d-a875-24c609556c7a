import uuid
from datetime import datetime
from urllib.parse import urlparse
import re

import sentry_sdk
from flask import Blueprint, request, render_template, flash, redirect, url_for, session, jsonify, current_app
from flask_jwt_extended import (
    get_jwt,
    create_refresh_token,
    jwt_required,
    get_jwt_identity,
    decode_token,
)
from flask_login import current_user
from flask_mail import Message
from flask_security import LoginForm, login_user, RegisterForm, logout_user
from flask_security.utils import verify_password, send_mail, hash_password
from sqlalchemy import func
from sqlalchemy.orm.attributes import flag_modified
from wtforms import fields
from wtforms.validators import InputRequired, Email

from app import db, csrf
from app.dashboard import ListHealthServices
from app.dashboard.extra_models import SiteConfigHealthServices
from app.pdf_convert.models import PasPt, RSession, RftRoutine
from app.qc.seeding import seed_qc_data
from app.site.models import Site
from common import mail
from config import Config
from .admin import UserAdmin, RoleAdminView, PermissionAdminView
from .models import User, Role, UserSites
from .token import create_user_access_token

auth_bp = Blueprint(
    "auth_bp", __name__, template_folder="templates", static_folder="static"
)


@auth_bp.route('/login/', methods=('GET', 'POST'))
@auth_bp.route('/login/<message>', methods=('GET', 'POST'))
def login_view(message=None):
    """
    Login view for a normal user
    :return:
    """
    session.clear()
    session.permanent = True
    login_route = url_for('auth_bp.login_view')
    if current_user.is_authenticated:
        return redirect("/admin/")
    form = LoginForm()
    if request.method == 'GET':
        return render_template(
            'security/login_user.html', title='Sign In', form=form, message=message,
            client="Rezibase"
        )
    if request.method == 'POST' and form.validate_on_submit():
        user = User.query.filter(
            func.lower(User.email) == form.email.data.lower(),
            User.roles.any(Role.name == 'user')
        ).first()
        if user is None:
            flash(u'Invalid email or User not in role user', 'auth')
            return redirect(login_route)
        if user is None or not user.verify_and_update_password(form.password.data):
            flash(u'Invalid password', 'auth')
            return redirect(login_route)
        if not user.sites:
            flash(u'User has no site set. Must assign site before login allowed.', 'auth')
            return render_template(
                'security/login_user.html', title='Sign In', form=form, message=message,
                client="Rezibase"
            )
        login_user(user, remember=form.remember.data, authn_via=["password"])
        user.fail_login_count = 0
        # after_this_request(view_commit)
        next_page = form.next.data
        if not next_page or urlparse(next_page).netloc != '':
            next_page = "/admin/"
        return redirect(next_page)

    try:
        user = User.query.filter(
            func.lower(User.email) == form.email.data.lower(),
            User.roles.any(Role.name == 'user')
        ).first()
        if user is not None:
            user.fail_login_count = (user.fail_login_count or 0) + 1
            # after_this_request(view_commit)
        if len(form.errors) > 0:
            sentry_sdk.capture_message(
                ' '.join([' '.join(x for x in errors) for errors in list(form.errors.values())])
            )
        flash(u'Invalid email, password or role user', 'auth')
    except:
        flash(u'Invalid email, password or role user', 'auth')
    return render_template(
        'security/login_user.html', title='Sign In', form=form, message=message,
        client="Rezibase"
    )


def is_valid_simple_email(email: str) -> bool:
    """Check for a normal '<EMAIL>' email format."""
    SIMPLE_EMAIL_REGEX = re.compile(r'^[^@\s]+@[^@\s]+\.[^@\s]+$')
    return bool(SIMPLE_EMAIL_REGEX.match(email))

def extract_domain(email: str) -> str:
    if not is_valid_simple_email(email):
        raise ValueError("Invalid email format")
    return email.rsplit('@', 1)[1].lower()


@auth_bp.route('/api/register', methods=['POST'])
def register_trial():
    data = request.get_json()

    if not data:
        return jsonify({'message': 'No input data provided'}), 400

    form = TrialRegistrationForm(data=data)

    if not form.validate():
        return jsonify({'message': 'Invalid data', 'errors': form.errors}), 400

    email = data.get('email', '').strip().lower()

    if User.query.filter(func.lower(User.email) == email).first():
        return jsonify({'message': 'Email already registered'}), 400

    if not is_valid_simple_email(email):
        return jsonify({'message': 'Invalid email format.'}), 400

    devuser_domains = User.get_devuser_domains()  # e.g., ['@cardiobase.com', '@carteblanche.tech']
    domain = extract_domain(email)

    reserved_domains = [d.lstrip('@').lower() for d in devuser_domains]

    if domain in reserved_domains:
        return jsonify({'message': 'Registration using internal system domains is not allowed.'}), 400

    if User.query.filter(func.lower(User.email) == email).first():
        return jsonify({'message': 'Email already registered'}), 400

    user_data = form.to_dict(False)

    site_name = user_data.pop("site_name").strip()
    site = Site(name=site_name, enable_otp=False)
    db.session.add(site)
    db.session.flush()

    site.copy_default_site_config(commit=False)

    max_code_subquery = db.session.query(func.coalesce(func.max(ListHealthServices.code), 0) + 1).scalar_subquery()
    hs = ListHealthServices(description=site_name, code=max_code_subquery)
    db.session.add(hs)
    db.session.flush()

    site_hs = SiteConfigHealthServices(hsid=hs.id, site_id=site.id, enabled=True)
    db.session.add(site_hs)

    user_meta = {}
    onboarding_demographics = data.get('onboarding_demographics')
    if onboarding_demographics:
        user_meta['onboarding_demographics'] = onboarding_demographics

    user = User(
        email=email,
        first_name=user_data['first_name'].strip(),
        last_name=user_data['last_name'].strip(),
        password=hash_password(user_data['password']),
        us_phone_number=user_data['us_phone_number'],
        fs_uniquifier=str(uuid.uuid4()),
        active=True,
        meta=user_meta,
    )
    db.session.add(user)
    db.session.flush()

    user_site = UserSites(user_id=user.id, site_id=site.id)
    db.session.add(user_site)

    patient = PasPt.create_fake(site.id)
    db.session.add(patient)
    db.session.flush()

    session = RSession.create_fake(patient.patientid)
    db.session.add(session)
    db.session.flush()

    rft = RftRoutine.create_fake(patient.patientid, session.sessionid)
    db.session.add(rft)

    seed_qc_data(site.id)
    db.session.commit()

    email_context = {
        'user': user,
        'site_name': site_name,
        'current_time': datetime.now(),
        'location': onboarding_demographics['country']
    }

    subject = render_template("admin/email/new_registration_subject.txt", **email_context)
    body = render_template("admin/email/new_registration_body.txt", **email_context)
    body_html = render_template("admin/email/new_registration_body.html", **email_context)

    msg = Message(
        subject=subject,
        sender=Config.MAIL_DEFAULT_SENDER,
        recipients=['<EMAIL>', '<EMAIL>', '<EMAIL>'],
        body=body,
        html=body_html,
    )
    mail.send(msg)

    access_token = create_user_access_token(user, mfa_required=user.totp_enabled)
    refresh_token = create_refresh_token(identity=str(user.id))

    return jsonify(
        {
            'access_token': access_token,
            'refresh_token': refresh_token,
            'user_id': user.id,
            'email': user.email,
            'mfaRequired': user.totp_enabled
        }
    )


@auth_bp.route('/api/login', methods=['POST'])
def login():
    data = request.get_json()
    requested_site_id = data.get('site_id', None)

    if not data or not data.get('email') or not data.get('password'):
        return jsonify({'message': 'Missing email or password'}), 400

    user = User.query.filter(func.lower(User.email) == data['email'].lower()).first()

    if not user:
        return jsonify({'message': 'Invalid email or password'}), 401

    if not user.active:
        return jsonify({'message': 'Account Inactive'}), 401

    site = Site.query.get(requested_site_id) if requested_site_id else None
    site_id = None
    if site is not None and (user.is_dev_user or site in user.sites):
        site_id = site.id

    if user and verify_password(data['password'], user.password):
        # Always set mfaRequired to True if user has TOTP enabled
        access_token = create_user_access_token(user, site_id=site_id, mfa_required=user.totp_enabled)
        refresh_token = create_refresh_token(identity=str(user.id))
        return jsonify(
            {
                'access_token': access_token,
                'refresh_token': refresh_token,
                'user_id': user.id,
                'email': user.email,
                'mfaRequired': user.totp_enabled
            }
        )

    return jsonify({'message': 'Invalid email or password'}), 401


@auth_bp.route('/api/refresh', methods=['POST'])
@jwt_required(refresh=True)
def refresh():
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)

    data = request.get_json(silent=True) or {}
    site_id = data.get('site_id')

    if site_id:
        access_token = create_user_access_token(user, site_id=site_id, mfa_required=False)
    else:
        access_token = create_user_access_token(user)

    return jsonify(
        {
            'access_token': access_token
        }
    )


@auth_bp.route('/api/me', methods=['GET'])
@jwt_required()
def get_profile():
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)

    if not user:
        return jsonify({'message': 'User not found'}), 404

    return jsonify(
        {
            'id': user.id,
            'email': user.email,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'password_reset_required': (user.meta or {}).get('password_reset_required', False),
        }
    )


class TrialRegistrationForm(RegisterForm):
    """
    Registration form for emeritus employees
    """
    site_name = fields.StringField(validators=[InputRequired()])
    first_name = fields.StringField(validators=[InputRequired()])
    last_name = fields.StringField()
    email = fields.StringField(validators=[InputRequired(), Email()])
    us_phone_number = fields.StringField(validators=[InputRequired()])
    password = fields.StringField(validators=[InputRequired()])


# @auth_bp.route('/register/', methods=('GET', 'POST'))
# def register_view():
#     """
#     Register view for a normal user
#     :return:
#     """
#     # from app.tasks import send_confirmation_email
#     form = UserRegistrationForm(request.form)
#     if request.method == 'POST' and form.validate():
#         data = form.to_dict(True)
#         data['active'] = False
#         data['roles'] = ['user', ]
#         user = register_user(form)
#         security.datastore.add_role_to_user(user, 'user')
#         security.datastore.deactivate_user(user)
#         db.session.commit()
#         # put false to get all non user model fields
#         data = form.to_dict(False)
#         # if not current_app.testing:
#         #     send_confirmation_email(user.uid, url_parse(request.base_url).netloc)
#         return redirect(url_for('auth_bp.welcome'))
#     return render_template('register.html', register_user_form=form)


@auth_bp.route('/logout/')
def logout_view():
    """
    Logout a user
    :return:
    """
    logout_user()
    session.clear()
    return redirect(url_for('auth_bp.login_view'))


@auth_bp.route('/api/users/', methods=['GET'])
@jwt_required()
def get_users():
    """
    Get all users
    :return:
    """
    jwt_claims = get_jwt()
    hasura_claims = jwt_claims.get('https://hasura.io/jwt/claims', {})
    site_id = hasura_claims.get('X-Hasura-Site-Id')
    if site_id:
        site_id = int(site_id)

    if site_id:
        users = User.query.filter(User.sites.any(id=site_id)).all()
    else:
        users = []  # Changed from {} to []

    users_dicts = [user.to_dict() for user in users]
    return jsonify(users_dicts)


@auth_bp.route('/api/users/current', methods=['GET'])
@jwt_required()
def get_current_user():
    """
    Get current user
    :return:
    """
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    if user:
        return jsonify(user.to_dict())
    else:
        return jsonify({"message": "No user logged in"}), 404


@auth_bp.route('/api/users/current', methods=['POST'])
@jwt_required()
def update_current_user():
    """
    Get current user
    :return:
    """
    data = request.get_json()
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)

    if user:
        user.meta = user.meta or {}
        if 'onboarding' in data:
            user.meta.update({'onboarding': data['onboarding']})
        flag_modified(user, 'meta')

        db.session.add(user)
        db.session.commit()
        return jsonify({"message": "User updated successfully"}), 200
    else:
        return jsonify({"message": "No user logged in"}), 404


# require login

@csrf.exempt
@auth_bp.route('/api/users/selected_lab', methods=['GET', 'POST'])
def get_selected_lab():
    """
    Get selected lab for a user
    :return:
    """

    if request.method == 'GET':
        return jsonify({'selected_lab': current_user.selected_lab_id})
    else:
        current_user.selected_lab_id = request.json['selected_lab_id']
        db.session.commit()
        return jsonify({'selected_lab': current_user.selected_lab_id})


@auth_bp.route('/approval_wait/')
def approval_wait():
    """
    Waiting for admin approval message after a user has submitted registration form
    :return:
    """
    return render_template('admin_approval.html')


@auth_bp.route('/welcome/')
def welcome():
    """
    Welcome message after a user has submitted registration form
    :return:
    """
    return render_template('welcome.html')


@auth_bp.route('/api/users/site/switch', methods=['POST'])
@jwt_required()
def api_site_switch():
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    data = request.get_json()
    site_id = data.get('site_id') if data else None

    if not site_id or not str(site_id).isnumeric():
        return jsonify({'error': 'Invalid or missing site_id'}), 400

    site_id = int(site_id)
    site = Site.query.get(site_id)

    if not site:
        return jsonify({'error': 'Site not found'}), 404

    if user.is_dev_user or site in user.sites:
        session['site_id'] = site.id
        session['site_text'] = site.name

        access_token = create_user_access_token(user, site_id=site.id)
        return jsonify(
            {
                'message': 'Site switched',
                'site_id': site.id,
                'site_text': site.name,
                'access_token': access_token
            }
        )
    else:
        return jsonify({'error': 'Not authorized for this site'}), 403


def add_admins(admin):
    admin.add_view(RoleAdminView(db.session, category='Users'))
    admin.add_view(UserAdmin(User, db.session, category='Users'))
    admin.add_view(PermissionAdminView(db.session, category='Users'))


@auth_bp.route('/api/change_password', methods=['POST'])
@jwt_required()
def change_password():
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    data = request.get_json()
    new_password = data.get('new_password')
    current_password = data.get('current_password')

    if not new_password:
        return jsonify({'message': 'New password is required'}), 400

    password_reset_required = (user.meta or {}).get('password_reset_required', False)

    if not password_reset_required:
        if not current_password:
            return jsonify({'current_password': 'Current password is required'}), 400
        if not verify_password(current_password, user.password):
            return jsonify({'current_password': 'Current password is incorrect'}), 400

    # Set new password and reset flag
    from flask_security import hash_password
    from sqlalchemy.orm.attributes import flag_modified
    from app import db

    user.password = hash_password(new_password)
    user.meta = user.meta or {}
    user.meta['password_reset_required'] = False
    flag_modified(user, 'meta')

    db.session.add(user)
    db.session.commit()
    return jsonify({'message': 'Password changed successfully'})


@auth_bp.route('/api/forgot-password/', methods=['POST'])
def api_forgot_password():
    data = request.get_json()
    if not data or not data.get('email'):
        return jsonify({'message': 'Email is required'}), 400

    user = User.query.filter(func.lower(User.email) == data['email'].lower()).first()

    if user:
        try:
            token = create_user_access_token(user)
            reset_link = url_for('home_bp.catch_all', token=token, _external=True).replace('/?', '/auth/reset-pass?')
            send_mail(
                subject="Reset your password",
                recipient=user.email,
                template="reset_instructions",
                reset_link=reset_link,
            )

            return jsonify(
                {
                    'message': 'Password reset instructions have been sent to your email.',
                }
            ), 200

        except Exception as e:
            sentry_sdk.capture_exception(e)
            current_app.logger.error(f"Error sending reset password email to {data.get('email')}: {e}")
            return jsonify({'message': 'Failed to send email. Please try again later.'}), 500

    return jsonify(
        {
            'message': 'If an account with this email exists, you will receive password reset instructions.',
        }
    ), 200


@auth_bp.route('/api/reset-password/', methods=['POST'])
def api_reset_password():
    """API endpoint to reset password using a token."""
    data = request.get_json()

    if not data:
        return jsonify({'message': 'No data provided'}), 400

    token = data.get('token')
    password = data.get('password')

    if not token or not password:
        return jsonify({'message': 'Token and password required'}), 400

    if len(password) < 8:
        return jsonify({'message': 'Password must be at least 8 characters'}), 400

    try:
        decoded = decode_token(token)
        user_id = decoded.get('sub')
        if not user_id:
            return jsonify({'message': 'Invalid token'}), 400

        user = User.query.get_or_404(user_id, description="Invalid reset token or user not found")

        user.password = hash_password(password)
        user.fail_login_count = 0
        user.meta = user.meta or {}
        user.meta['password_reset_required'] = False
        flag_modified(user, 'meta')

        db.session.commit()
        return jsonify({'message': 'Password reset successfully'}), 200

    except Exception as e:
        sentry_sdk.capture_exception(e)
        db.session.rollback()
        return jsonify({'message': 'Failed to reset password'}), 500
