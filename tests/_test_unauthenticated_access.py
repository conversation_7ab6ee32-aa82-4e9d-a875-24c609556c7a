"""
Tests that all API endpoints require authentication.
This test will fail if an endpoint can be accessed without authentication,
unless it is explicitly included in the public_endpoints list.
"""

from http import HTTPStatus
from flask import current_app, url_for


def test_unauthenticated_access(client):
    errors = []

    public_endpoints = [
    ################# OK
    ('GET', '/login/'),                    # auth_bp.login_view
    ('POST', '/login/'),                   # auth_bp.login_view
    ('GET', '/login/1'),                   # auth_bp.login_view
    ('POST', '/login/1'),                  # auth_bp.login_view
    ('POST', '/api/login'),                # auth_bp.login
    ('GET', '/welcome/'),                  # auth_bp.welcome
    ('GET', '/webhook/health'),            # activity_logs_bp.webhook_health
    ('POST', '/api/forgot-password/'),     # auth_bp.api_forgot_password
    ('POST', '/api/reset-password/'),      # auth_bp.api_reset_password
    ('GET', '/1'),                         # home_bp.catch_all
    ('GET', '/admin/login/'),              # security.login
    ('POST', '/admin/login/'),             # security.login
    ('GET', '/admin/reset'),               # security.forgot_password
    ('POST', '/admin/reset'),              # security.forgot_password
    ('GET', '/admin/confirm'),             # security.send_confirmation
    ('POST', '/admin/confirm'),            # security.send_confirmation


    ################# BAD?
    ('POST', '/api/register'),             # auth_bp.register_trial
    ('GET', '/approval_wait/'),            # auth_bp.approval_wait
    ('POST', '/webhook/activity'),         # activity_logs_bp.activity_webhook
    ('GET', '/docs'),                      # apifairy.docs
    ('GET', '/api/users/selected_lab'),    # auth_bp.get_selected_lab
    ('POST', '/api/users/selected_lab'),   # auth_bp.get_selected_lab
    ('GET', '/admin/controlmethod/new/'),  # controlmethod.create_view
    ('POST', '/admin/controlmethod/new/'), # controlmethod.create_view
    ('GET', '/admin/controlmethod/'),      # controlmethod.index_view
    ('GET', '/admin/controlrule/'),        # controlrule.index_view
    ('GET', '/admin/equipment/new/'),      # equipment.create_view
    ('POST', '/admin/equipment/new/'),     # equipment.create_view
    ('GET', '/admin/equipment/'),          # equipment.index_view
    ('GET', '/admin/equipmentcompany/new/'), # equipmentcompany.create_view
    ('POST', '/admin/equipmentcompany/new/'), # equipmentcompany.create_view
    ('GET', '/admin/equipmentcompany/'),   # equipmentcompany.index_view
    ('POST', '/api/attachments/download'), # file_bp.download_attachment
    ('GET', '/api/pdf_header'),            # file_bp.get_pdf_header
    ('POST', '/api/s3_file_url'),          # file_bp.get_presigned_url
    ('POST', '/api/attachments/list'),     # file_bp.list_attachments
    ('POST', '/api/s3_list_files'),        # file_bp.list_s3_files
    ('POST', '/api/pdf_import'),           # file_bp.pdf_import
    ('POST', '/api/pdf_import/confirm'),   # file_bp.pdf_import_confirm
    ('POST', '/api/pdf_import/patient'),   # file_bp.pdf_import_patient
    ('POST', '/api/pdf_import/undo'),      # file_bp.undo_pdf_import
    ('POST', '/api/attachments/upload'),   # file_bp.upload_attachment
    ('POST', '/api/rft_report'),           # file_bp.rft_report
    ('GET', '/admin/lab/new/'),            # lab.create_view
    ('POST', '/admin/lab/new/'),           # lab.create_view
    ('GET', '/admin/lab/'),                # lab.index_view
    ('GET', '/admin/listlanguage/new/'),   # listlanguage.create_view
    ('POST', '/admin/listlanguage/new/'),  # listlanguage.create_view
    ('GET', '/admin/listlanguage/'),       # listlanguage.index_view
    ('GET', '/admin/listnationality/new/'), # listnationality.create_view
    ('POST', '/admin/listnationality/new/'), # listnationality.create_view
    ('GET', '/admin/listnationality/'),    # listnationality.index_view
    ('GET', '/admin/parameter/new/'),      # parameter.create_view
    ('POST', '/admin/parameter/new/'),     # parameter.create_view
    ('GET', '/admin/parameter/'),          # parameter.index_view
    ('GET', '/admin/permission/new/'),     # permission.create_view
    ('POST', '/admin/permission/new/'),    # permission.create_view
    ('GET', '/admin/permission/'),         # permission.index_view
    ('GET', '/admin/prefsfielditems/new/'), # prefsfielditems.create_view
    ('POST', '/admin/prefsfielditems/new/'), # prefsfielditems.create_view
    ('GET', '/admin/prefsfielditems/'),    # prefsfielditems.index_view
    ('GET', '/admin/prefsfields/new/'),    # prefsfields.create_view
    ('POST', '/admin/prefsfields/new/'),   # prefsfields.create_view
    ('GET', '/admin/prefsfields/'),        # prefsfields.index_view
    ('GET', '/alerts'),                    # qc_bp.alerts_page
    ('GET', '/config'),                    # qc_bp.config_route
    ('GET', '/devreport'),                 # qc_bp.devreport_route
    ('GET', '/equipments'),                # qc_bp.equipments
    ('GET', '/parameters'),                # qc_bp.parameters
    ('GET', '/ps'),                        # qc_bp.ps_route
    ('GET', '/admin/role/new/'),           # role.create_view
    ('POST', '/admin/role/new/'),          # role.create_view
    ('GET', '/admin/role/'),               # role.index_view
    ('GET', '/admin/sidebarmodule/new/'),  # sidebarmodule.create_view
    ('POST', '/admin/sidebarmodule/new/'), # sidebarmodule.create_view
    ('GET', '/admin/sidebarmodule/'),      # sidebarmodule.index_view
    ('GET', '/admin/site/'),               # site.index_view
    ('GET', '/admin/sitesettings/new/'),   # sitesettings.create_view
    ('POST', '/admin/sitesettings/new/'),  # sitesettings.create_view
    ('GET', '/admin/sitesettings/'),       # sitesettings.index_view
    ('GET', '/admin/user/'),               # user.index_view
    ('GET', '/admin/user/invite/'),        # user.invite_user_view
    ('POST', '/admin/user/invite/'),       # user.invite_user_view
    ('GET',  '/apispec.json')
    ]

    db = current_app.extensions['sqlalchemy']

    for rule in current_app.url_map.iter_rules():

        # Provide dummy arguments for URL generation
        kwargs = {arg: 1 for arg in rule.arguments}
        url = url_for(rule.endpoint, **kwargs)

        for method in rule.methods:
            if method in ['OPTIONS', 'HEAD']:
                continue

            if (method, url) in public_endpoints:
                continue

            # Prepare request data for specific endpoints that require it
            json_data = None
            if method == 'POST' and url.endswith('/api/users/selected_lab'):
                json_data = {'selected_lab_id': 1} 
            if method == 'POST' and url.endswith('/api/s3_file_url'):
                json_data = {'key': 'sdfasdfas'} 
            if method == 'POST' and url.endswith('/api/s3_list_files'):
                json_data = {'prefix': ''} 

            db.session.rollback()                

            try:
                response = client.open(url, method=method, json=json_data)
            except Exception as e:
                errors.append(f"Endpoint {rule.endpoint} ({method} {url}) did not return an expected status due to template error: {e}")
                continue
                
            declined_status_codes = [HTTPStatus.UNAUTHORIZED, HTTPStatus.NOT_FOUND, HTTPStatus.FORBIDDEN, HTTPStatus.FOUND, HTTPStatus.SEE_OTHER]
            if response.status_code not in declined_status_codes:
                errors.append(f"Endpoint {rule.endpoint} ({method} {url}) did not return an expected status. Got {response.status_code}")

    assert not errors, "\n".join(errors)
