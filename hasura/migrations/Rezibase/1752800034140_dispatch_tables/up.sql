--This takes the status messages that get generated out on site.
CREATE TABLE public.dispatch_status_log (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    message_id TEXT NOT NULL, -- Not of the status message, but of the message the status relates to.
    correlation_id TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    producer_id TEXT,
    severity TEXT NOT NULL,
    message_category TEXT NOT NULL,
    text TEXT NOT NULL,
    site_id INT NOT NULL,
    CONSTRAINT dispatch_status_log_site_id_fkey FOREIGN KEY (site_id) REFERENCES public.site(id)
);

--For anything that goes to dispatch, a record is created in here. If we pick it up manually, then we put it in here, too.
--TODO what if messages arrive out of order? ie. we get a status before it's created?
CREATE TABLE public.dispatch_record (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY, 
    message_id VARCHAR(255) NOT NULL UNIQUE,         -- HL7 Message Control ID (MSH-10) and workflow-wide identifier

    source VARCHAR(50) NOT NULL,               -- 'rezibase', 'manual', etc.
    status VARCHAR(50) NOT NULL,               -- 'pending', 'dispatched', 'acknowledged', 'failed'
    stage VARCHAR(50),                         -- 'construction', 'hl7_sent', 'fax_sent', etc.
    failure_reason TEXT,                       -- Detailed error message
    is_resolved BOOLEAN DEFAULT FALSE,         -- Manual resolution flag
    notes TEXT,                                -- Admin/support notes
    attempts INT DEFAULT 0,                    -- Dispatch attempts

    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),

    -- Dispatch Metadata (multi-channel delivery targets)
    filename VARCHAR(255),
    base64_data TEXT,
    edi VARCHAR(50),
    provider_number VARCHAR(50),
    fax_number VARCHAR(50),

    -- GoFax integration (optional linkage)
    gofax_outbound_id UUID,                    -- Reference to fax_outbound.id (nullable)

    -- HL7 acknowledgment details
    ack_code VARCHAR(10),                      -- HL7 ACK code (e.g., AA, AE, AR)
    ack_message VARCHAR(1000),                 -- HL7 ACK message

    -- Clinical & Patient Metadata
    record_id VARCHAR(255),
    ur VARCHAR(50),
    site_id INT NOT NULL,
    user_id VARCHAR(255),                      -- User or system initiating the dispatch

    -- Optional link to interface queue/log (nullable for non-HL7 dispatches)
    interface_queue_id INT NULL,               -- HL7 queue link if applicable
    interface_log_id INT NULL,                 -- HL7 ACK log link if applicable

    CONSTRAINT dispatch_record_log_site_id_fkey FOREIGN KEY (site_id) REFERENCES public.site(id),

    -- Foreign Key Constraints (optional FKs for HL7 pipeline tracking)
    CONSTRAINT fk_interface_queue FOREIGN KEY (interface_queue_id) REFERENCES interface_queue (interface_queue_id),
    CONSTRAINT fk_interface_log FOREIGN KEY (interface_log_id) REFERENCES interface_log (interface_log_id)
);

CREATE TRIGGER set_dispatch_record_updated_at
BEFORE UPDATE ON public.dispatch_record
FOR EACH ROW
EXECUTE FUNCTION public.set_current_timestamp_updated_at();

CREATE TRIGGER set_dispatch_status_log_updated_at
BEFORE UPDATE ON public.dispatch_status_log
FOR EACH ROW
EXECUTE FUNCTION public.set_current_timestamp_updated_at();
