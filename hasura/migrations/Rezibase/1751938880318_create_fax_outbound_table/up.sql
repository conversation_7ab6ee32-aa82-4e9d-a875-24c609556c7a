CREATE TABLE public.fax_outbound (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    created_at timestamp without time zone NOT NULL DEFAULT now(),
    updated_at timestamp without time zone NOT NULL DEFAULT now(),
    site_id integer NOT NULL,
    message_id character varying NOT NULL UNIQUE,
    
    -- TODO - I'm not sure how these are different.
    report_file_content_data text NOT NULL,
    file_content text,

    -- TODO - What is this?
    "timestamp" timestamp without time zone NOT NULL,

    client character varying,
    test_type character varying,
    record_id character varying,
    ur character varying,
    recipient_fax character varying NOT NULL,
    file_name character varying,
    correlation_id character varying,
    producer_id character varying,
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'failed', 'completed')),
    gofax_id integer,
    gofax_status character varying,
    gofax_error text,
    status_attempts integer DEFAULT 0 NOT NULL,
    raw_message jsonb,
    CONSTRAINT fax_outbound_site_id_fkey FOREIGN KEY (site_id) REFERENCES public.site(id)
);

CREATE TRIGGER set_fax_outbound_updated_at
BEFORE UPDATE ON public.fax_outbound
FOR EACH ROW
EXECUTE FUNCTION public.set_current_timestamp_updated_at();
