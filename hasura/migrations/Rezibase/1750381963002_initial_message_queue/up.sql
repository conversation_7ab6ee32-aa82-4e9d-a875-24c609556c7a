CREATE TABLE public.message_incoming_queue (
    id BIGSERIAL PRIMARY KEY,
    site_id BIGINT NOT NULL,
    message_id TEXT NOT NULL,
    queue_name TEXT NOT NULL,
    payload JSONB NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'failed', 'completed')),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    CONSTRAINT fk_message_incoming_queue_site FOREIGN KEY (site_id) REFERENCES site(id)
);

CREATE INDEX idx_message_incoming_queue_status ON public.message_incoming_queue(status);
CREATE INDEX idx_message_incoming_queue_name ON public.message_incoming_queue(queue_name);
CREATE INDEX idx_message_incoming_queue_site_id ON public.message_incoming_queue(site_id);
CREATE INDEX idx_message_incoming_queue_name_status ON public.message_incoming_queue(queue_name, status);

CREATE TABLE public.message_outgoing_queue (
    id BIGSERIAL PRIMARY KEY,
    site_id BIGINT NOT NULL,
    message_id TEXT NOT NULL,
    queue_name TEXT NOT NULL,
    payload JSONB NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'awaiting_ack', 'failed', 'completed')),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    attempts INTEGER NOT NULL DEFAULT 0,
    error_message TEXT,
    CONSTRAINT fk_message_outgoing_queue_site FOREIGN KEY (site_id) REFERENCES site(id)
);

CREATE INDEX idx_message_outgoing_queue_status ON public.message_outgoing_queue(status);
CREATE INDEX idx_message_outgoing_queue_name ON public.message_outgoing_queue(queue_name);
CREATE INDEX idx_message_outgoing_queue_site_id ON public.message_outgoing_queue(site_id);
CREATE INDEX idx_message_outgoing_queue_name_status ON public.message_outgoing_queue(queue_name, status);

CREATE OR REPLACE FUNCTION public.set_current_timestamp_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  -- Only update if updated_at wasn't already changed in this operation
  IF NEW.updated_at = OLD.updated_at THEN
    NEW.updated_at = NOW();
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER set_message_incoming_queue_updated_at
BEFORE UPDATE ON public.message_incoming_queue
FOR EACH ROW
EXECUTE FUNCTION public.set_current_timestamp_updated_at();

CREATE TRIGGER set_message_outgoing_queue_updated_at
BEFORE UPDATE ON public.message_outgoing_queue
FOR EACH ROW
EXECUTE FUNCTION public.set_current_timestamp_updated_at();
